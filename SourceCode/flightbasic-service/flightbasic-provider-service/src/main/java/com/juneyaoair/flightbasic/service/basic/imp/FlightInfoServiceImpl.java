package com.juneyaoair.flightbasic.service.basic.imp;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.dao.basic.FlightInfoMysqlServiceDao;
import com.juneyaoair.flightbasic.dao.basic.FlightInfoServiceDao;
import com.juneyaoair.flightbasic.exception.CommonException;
import com.juneyaoair.flightbasic.mapper.api.TRouteLabelMapper;
import com.juneyaoair.flightbasic.mapstruct.FlightInfoDTOMapper;
import com.juneyaoair.flightbasic.mapstruct.RouteLabelMapStruct;
import com.juneyaoair.flightbasic.model.basic.FlightInfoPO;
import com.juneyaoair.flightbasic.model.basic.TrrFlightInfoPO;
import com.juneyaoair.flightbasic.mysql.model.TFlightInfoPO;
import com.juneyaoair.flightbasic.redis.FlightBasicRedisUtil;
import com.juneyaoair.flightbasic.redis.RedisKeyConstants;
import com.juneyaoair.flightbasic.request.airLine.ParamSpaAddonDTO;
import com.juneyaoair.flightbasic.request.airLine.ReqSpaAddonDTO;
import com.juneyaoair.flightbasic.request.flightInfo.FlightExistDateReqDTO;
import com.juneyaoair.flightbasic.request.flightInfo.QueryFlightByCityAndDate;
import com.juneyaoair.flightbasic.request.trrFlightInfo.ParamTrrFlightInfo;
import com.juneyaoair.flightbasic.response.airline.SpaAddonResDTO;
import com.juneyaoair.flightbasic.response.flightInfo.FlightExistDateDTO;
import com.juneyaoair.flightbasic.response.flightInfo.FlightInfoDTO;
import com.juneyaoair.flightbasic.response.trrFlightInfo.TrrFlightInfoDTO;
import com.juneyaoair.flightbasic.route.RouteLabelBO;
import com.juneyaoair.flightbasic.route.RouteLabelDTO;
import com.juneyaoair.flightbasic.service.airline.AirlineExpandService;
import com.juneyaoair.flightbasic.service.basic.FlightInfoService;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FlightInfoServiceImpl implements FlightInfoService {

    @Value("${flightInfo.Source:mysql}")
    private String flightInfoSource;
    @Autowired
    private FlightBasicRedisUtil flightBasicRedisUtil;
    @Autowired
    private FlightInfoServiceDao flightinfoServiceDao;
    @Autowired
    private FlightInfoMysqlServiceDao flightInfoMySqlServiceDao;
    @Autowired
    private AirlineExpandService airlineExpandService;
    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private TRouteLabelMapper routeLabelMapper;

    @Override
    public List<FlightInfoDTO> searchFlightInfoList(FlightInfoPO flightInfo) {
        if (StringUtils.isAllBlank(flightInfo.getFlightNo()
                , flightInfo.getFlightDate()
                , flightInfo.getDepAirport()
                , flightInfo.getArrAirport()
                , flightInfo.getDepCity()
                , flightInfo.getArrCity())
        ) {
            throw new CommonException(WSEnum.ERROR.resultCode, "缺少必要的查询参数");
        }
        //目前被调用端存在不规范的格式，截取为yyyy-MM-dd格式
        if (StringUtils.isNotBlank(flightInfo.getFlightDate())) {
            flightInfo.setFlightDate(flightInfo.getFlightDate().substring(0, 10));
        }
        // 是否查询单个指定航班信息
        boolean singleAirportFlight = false;
        String redisKey = null;
        //机场查询
        if (StringUtils.isNoneBlank(flightInfo.getFlightNo(), flightInfo.getFlightDate(), flightInfo.getDepAirport(), flightInfo.getArrAirport())) {
            singleAirportFlight = true;
            redisKey = RedisKeyConstants.SINGLE_FLIGHT_INFO + flightInfo.getFlightDate() + ":" + flightInfo.getFlightNo()
                    + "_" + flightInfo.getDepAirport() + "_" + flightInfo.getArrAirport() + "_" + flightInfo.hashCode();
            String redisValue = flightBasicRedisUtil.get(redisKey);
            if (StringUtils.isNotBlank(redisValue)) {
                TypeReference<List<FlightInfoDTO>> typeReference = new TypeReference<List<FlightInfoDTO>>() {
                };
                return JSON.parseObject(redisValue, typeReference);
            }
        }
        //城市查询
        boolean singleCityFlight = false;
        String redisCityKey = null;
        if (StringUtils.isNoneBlank(flightInfo.getFlightNo(), flightInfo.getFlightDate(), flightInfo.getDepCity(), flightInfo.getArrCity())) {
            singleCityFlight = true;
            redisCityKey = RedisKeyConstants.SINGLE_FLIGHT_INFO_CITY + flightInfo.getFlightDate() + ":" + flightInfo.getFlightNo()
                    + "_" + flightInfo.getDepCity() + "_" + flightInfo.getArrCity() + "_" + flightInfo.hashCode();
            String redisValue = flightBasicRedisUtil.get(redisCityKey);
            if (StringUtils.isNotBlank(redisValue)) {
                TypeReference<List<FlightInfoDTO>> typeReference = new TypeReference<List<FlightInfoDTO>>() {
                };
                return JSON.parseObject(redisValue, typeReference);
            }
        }
        List<FlightInfoDTO> resultList = new ArrayList<>();
        if ("mysql".equals(flightInfoSource)) {
            List<TFlightInfoPO> flightInfoList = flightInfoMySqlServiceDao.searchFlightInfoList(flightInfo);
            flightInfoList.forEach(tFlightInfo -> {
                FlightInfoDTO flightInfoDTO = FlightInfoDTOMapper.convertToFlightInfoDTO(tFlightInfo);
                resultList.add(flightInfoDTO);
            });
        } else {
            // 其他情况从mongo中获取
            List<FlightInfoPO> pos = flightinfoServiceDao.searchflightInfoList(flightInfo);
            for (FlightInfoPO po : pos) {
                FlightInfoDTO flightInfoDTO = new FlightInfoDTO();
                BeanUtils.copyNotNullProperties(po, flightInfoDTO);
                resultList.add(flightInfoDTO);
            }
        }
        if (singleAirportFlight && StringUtils.isNotBlank(redisKey) && CollectionUtils.isNotEmpty(resultList)) {
            flightBasicRedisUtil.set(redisKey, JSON.toJSONString(resultList), 30 * 60L);
        }
        if (singleCityFlight && StringUtils.isNotBlank(redisCityKey) && CollectionUtils.isNotEmpty(resultList)) {
            flightBasicRedisUtil.set(redisCityKey, JSON.toJSONString(resultList), 30 * 60L);
        }
        return resultList;
    }

    @Override
    public List<TrrFlightInfoDTO> searchTrrFlightInfo(BaseRequestDTO<ParamTrrFlightInfo> requestDTO) {
        List<TrrFlightInfoDTO> trrFlightInfoDTO = new ArrayList<>();

        ParamTrrFlightInfo param = requestDTO.getRequest();
        TrrFlightInfoPO trrFlightInfoPO = new TrrFlightInfoPO();
        trrFlightInfoPO.setFlightNo(param.getFlightNo());
        trrFlightInfoPO.setFlightDate(param.getFlightDate());
        trrFlightInfoPO.setDepAirport(param.getDepAirport());
        trrFlightInfoPO.setArrAirport(param.getArrAirport());
        List<TrrFlightInfoPO> trrFlightInfoPOS = flightinfoServiceDao.searchTrrFlightInfo(trrFlightInfoPO);

        TrrFlightInfoDTO tempDTO = null;
        for (TrrFlightInfoPO po : trrFlightInfoPOS) {
            tempDTO = new TrrFlightInfoDTO();
            BeanUtil.copyProperties(po, tempDTO);
            trrFlightInfoDTO.add(tempDTO);
        }
        return trrFlightInfoDTO;
    }

    @Override
    public List<FlightInfoDTO> getFlightByCityAndDate(QueryFlightByCityAndDate queryFlightByCityAndDate) {
        List<FlightInfoDTO> resultList = new ArrayList<>();
        List<TFlightInfoPO> pos = flightInfoMySqlServiceDao.getFlightByCityAndDate(queryFlightByCityAndDate);
        for (TFlightInfoPO po : pos) {
            FlightInfoDTO flightInfoDTO = FlightInfoDTOMapper.convertToFlightInfoDTO(po);
            resultList.add(flightInfoDTO);
        }
        return resultList;
    }

    @Override
    public FlightInfoDTO getFlightInfo(String flightDate, String flightNo, String depAirportCode, String arrAirportCode) {
        FlightInfoPO flightInfo = new FlightInfoPO();
        flightInfo.setFlightDate(flightDate);
        flightInfo.setFlightNo(flightNo);
        flightInfo.setDepAirport(depAirportCode);
        flightInfo.setArrAirport(arrAirportCode);
        List<FlightInfoDTO> flightInfoList = searchFlightInfoList(flightInfo);
        if (CollectionUtils.isEmpty(flightInfoList)) {
            return null;
        }
        return flightInfoList.get(0);
    }

    @Override
    public List<FlightExistDateDTO> existFlightByCityAndDate(FlightExistDateReqDTO flightExistDateReqDTO) {
        List<String> dateStrList = new ArrayList<>();
        LocalDate localDate = LocalDate.now();
        dateStrList.add(DateUtils.getDateString(localDate, DateUtils.STRING_FORMAT_YYYY_MM_DD));
        //默认查询500天
        for (int i = 1; i <= 500; i++) {
            LocalDate currentDate = localDate.plusDays(i);
            dateStrList.add(DateUtils.getDateString(currentDate, DateUtils.STRING_FORMAT_YYYY_MM_DD));
        }
        String redisKey = RedisKeyConstants.FLIGHT_CALENDAR_CLICK + flightExistDateReqDTO.getDepCityCode() + flightExistDateReqDTO.getArrCityCode();
        List<String> stringList = flightBasicRedisUtil.getHashValues(redisKey);
        List<FlightExistDateDTO> flightExistDateDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(stringList)) {
            for (String str : stringList) {
                FlightExistDateDTO flightExistDateDTO = JsonUtil.fromJson(str, FlightExistDateDTO.class);
                flightExistDateDTOList.add(flightExistDateDTO);
            }
            flightExistDateDTOList = flightExistDateDTOList.stream().sorted(Comparator.comparing(FlightExistDateDTO::getDate)).collect(Collectors.toList());
            return flightExistDateDTOList;
        }
        //查询直达航班
        Callable<List<FlightInfoDTO>> callableOwn = () -> {
            QueryFlightByCityAndDate queryFlightByCityAndDate = new QueryFlightByCityAndDate();
            queryFlightByCityAndDate.setDepCity(flightExistDateReqDTO.getDepCityCode());
            queryFlightByCityAndDate.setArrCity(flightExistDateReqDTO.getArrCityCode());
            queryFlightByCityAndDate.setFlightDateList(dateStrList);
            List<FlightInfoDTO> ownFlightInfoDTOList = getFlightByCityAndDate(queryFlightByCityAndDate);
            return ownFlightInfoDTOList;
        };
        //查询spa航班以及addon航班
        Callable<List<FlightInfoDTO>> callableSpaAddon = () -> {
            List<FlightInfoDTO> spaAddonFlightInfoList = new ArrayList<>();
            //查询spa航班以及addon航班
            ReqSpaAddonDTO param = getReqSpaAddonDTO(flightExistDateReqDTO, dateStrList);
            BaseResultDTO<List<SpaAddonResDTO>> baseResultDTO = airlineExpandService.searchSpaAddonLine(param);
            if (CollectionUtils.isNotEmpty(baseResultDTO.getResult())) {
                FlightInfoDTO flightInfoDTO;
                for (SpaAddonResDTO spaAddonResDTO : baseResultDTO.getResult()) {
                    flightInfoDTO = new FlightInfoDTO();
                    flightInfoDTO.setFlightDate(spaAddonResDTO.getFlightDate());
                    flightInfoDTO.setFlightNo(spaAddonResDTO.getCombFlightNo());
                    flightInfoDTO.setDepCity(spaAddonResDTO.getDepCityCode());
                    flightInfoDTO.setArrCity(spaAddonResDTO.getArrCityCode());
                    flightInfoDTO.setDepAirport(spaAddonResDTO.getDepAirport());
                    flightInfoDTO.setArrAirport(spaAddonResDTO.getArrAirport());
                    spaAddonFlightInfoList.add(flightInfoDTO);
                }
            }
            return spaAddonFlightInfoList;
        };
        Future<List<FlightInfoDTO>> future = threadPoolTaskExecutor.submit(callableOwn);
        Future<List<FlightInfoDTO>> future2 = threadPoolTaskExecutor.submit(callableSpaAddon);
        List<FlightInfoDTO> allFlightInfoList = new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(future.get())) {
                allFlightInfoList.addAll(future.get());
            }
            if (CollectionUtils.isNotEmpty(future2.get())) {
                allFlightInfoList.addAll(future2.get());
            }
        } catch (Exception e) {
            log.error("异步查询航班信息异常:", e);
        }
        FlightExistDateDTO flightExistDateDTO;
        for (String dateStr : dateStrList) {
            flightExistDateDTO = new FlightExistDateDTO();
            flightExistDateDTO.setDate(dateStr);
            flightExistDateDTO.setExistFlight(allFlightInfoList.stream().anyMatch(flightInfoDTO -> flightInfoDTO.getFlightDate().equals(dateStr)));
            flightExistDateDTOList.add(flightExistDateDTO);
        }
        flightExistDateDTOList = flightExistDateDTOList.stream().sorted(Comparator.comparing(FlightExistDateDTO::getDate)).collect(Collectors.toList());
        //容错处理，如果都是不可点击的默认恢复成可点状态
        if (flightExistDateDTOList.stream().allMatch(flightExistDate -> !flightExistDate.isExistFlight())) {
            for (FlightExistDateDTO flightExistDate : flightExistDateDTOList) {
                flightExistDate.setExistFlight(true);
            }
        }
        Map<String, String> flightExistDateDTOMap = flightExistDateDTOList.stream().collect(Collectors.toMap(FlightExistDateDTO::getDate, obj -> JsonUtil.objectToJson(obj)));
        flightBasicRedisUtil.putHashAll(redisKey, flightExistDateDTOMap, 4 * 3600L);
        return flightExistDateDTOList;
    }

    @Override
    public List<RouteLabelDTO> queryRouteLabel(BaseRequestDTO baseRequestDTO) {
        List<RouteLabelBO> boList = routeLabelMapper.selectListBO();

        // RouteLabelBO convert to RouteLabelDTO
        return boList.stream().map(RouteLabelMapStruct.INSTANCE::dtoToRouteLabelDTO)
                // sort
                .sorted(Comparator.comparingInt(RouteLabelDTO::getSortNum)).collect(Collectors.toList());

    }

    private ReqSpaAddonDTO getReqSpaAddonDTO(FlightExistDateReqDTO flightExistDateReqDTO, List<String> dateStrList) {
        ReqSpaAddonDTO param = new ReqSpaAddonDTO();
        List<ParamSpaAddonDTO> paramSpaAddonDTOList = new ArrayList<>();
        ParamSpaAddonDTO paramSpaAddonDTO;
        for (String dateStr : dateStrList) {
            paramSpaAddonDTO = new ParamSpaAddonDTO();
            paramSpaAddonDTO.setFlightDate(dateStr);
            paramSpaAddonDTO.setDepCityCode(flightExistDateReqDTO.getDepCityCode());
            paramSpaAddonDTO.setArrCityCode(flightExistDateReqDTO.getArrCityCode());
            paramSpaAddonDTOList.add(paramSpaAddonDTO);
        }
        param.setSpaAddons(paramSpaAddonDTOList);
        return param;
    }
}
