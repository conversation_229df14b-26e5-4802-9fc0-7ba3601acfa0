package com.juneyaoair.flightbasic.controller;

import com.juneyaoair.flightbasic.BaseController;
import com.juneyaoair.flightbasic.commondto.RequestData;
import com.juneyaoair.flightbasic.commondto.ResponseData;
import com.juneyaoair.flightbasic.request.airLine.AttentionFlightParam;
import com.juneyaoair.flightbasic.request.airLine.CancelAttentionFlightParam;
import com.juneyaoair.flightbasic.request.airLine.QueryAttentionFlightParam;
import com.juneyaoair.flightbasic.response.airline.FollowAirLineResDTO;
import com.juneyaoair.flightbasic.service.basic.FollowAirLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/21 11:10
 */
@Api(value = "关注航班管理")
@Slf4j
@RestController
@RequestMapping(value = "/attentionFlight")
public class AttentionFlightController extends BaseController {
    @Autowired
    private FollowAirLineService followAirLineService;

    @ApiOperation(value = "查询关注航班列表")
    @PostMapping(value = "/queryAttentionFlightList")
    public ResponseData<List<FollowAirLineResDTO>> queryAttentionFlightList(@RequestBody @Validated RequestData requestData, BindingResult bindingResult) {
        checkParam(bindingResult);
        return ResponseData.success(followAirLineService.queryFollowAirLine(requestData.getFfpId(), requestData.getFfpNo()));
    }

    @ApiOperation(value = "增加关注航班")
    @PostMapping(value = "/addAttentionFlight")
    public ResponseData addAttentionFlight(@RequestBody @Validated RequestData<AttentionFlightParam> requestData, BindingResult bindingResult) {
        checkParam(requestData.getData(), bindingResult);
        return ResponseData.success(followAirLineService.addorUpdateAirline(requestData.getFfpId(), requestData.getFfpNo(), requestData.getData()));
    }

    @ApiOperation(value = "取消关注航班")
    @PostMapping(value = "/cancelAttentionFlight")
    public ResponseData cancelAttentionFlight(@RequestBody @Validated RequestData<CancelAttentionFlightParam> requestData, BindingResult bindingResult) {
        checkParam(requestData.getData(), bindingResult);
        followAirLineService.cancelAirline(requestData.getFfpId(), requestData.getFfpNo(), requestData.getData());
        return ResponseData.success();
    }

    @ApiOperation(value = "关注航班详细")
    @PostMapping(value = "/queryAttentionDetail")
    public ResponseData<FollowAirLineResDTO> queryAttentionDetail(@RequestBody @Validated RequestData<QueryAttentionFlightParam> requestData, BindingResult bindingResult) {
        checkParam(requestData.getData(), bindingResult);
        return ResponseData.success(followAirLineService.queryAttentionDetail(requestData.getFfpId(), requestData.getFfpNo(), requestData.getData()));
    }
}
