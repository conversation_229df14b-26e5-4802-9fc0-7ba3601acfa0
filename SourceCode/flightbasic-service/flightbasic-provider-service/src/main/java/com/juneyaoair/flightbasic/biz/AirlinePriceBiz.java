package com.juneyaoair.flightbasic.biz;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.juneyaoair.flightbasic.appenum.ChannelCodeEnum;
import com.juneyaoair.flightbasic.appenum.InterFlagEnum;
import com.juneyaoair.flightbasic.common.UnifiedOrderResultEnum;
import com.juneyaoair.flightbasic.external.config.ThirdUrlConfig;
import com.juneyaoair.flightbasic.external.service.EtermService;
import com.juneyaoair.flightbasic.flight.ConnectFlightCombInfo;
import com.juneyaoair.flightbasic.flight.ConnectFlightCombInfoD;
import com.juneyaoair.flightbasic.inner.service.BasicDataService;
import com.juneyaoair.flightbasic.mongo.dao.SPAAddonLineDao;
import com.juneyaoair.flightbasic.mongo.po.SPAAddonLine;
import com.juneyaoair.flightbasic.redis.MobileAPIRedisUtil;
import com.juneyaoair.flightbasic.redis.RedisKeyConstants;
import com.juneyaoair.flightbasic.response.airport.AirPortInfoDTO;
import com.juneyaoair.flightbasic.response.av.FlightInfoCombApi;
import com.juneyaoair.flightbasic.response.av.PtQueryFlightFareResponse;
import com.juneyaoair.flightbasic.response.av.V2CabinFare;
import com.juneyaoair.flightbasic.response.av.V2FlightInfo;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/9/2 14:24
 */
@Slf4j
@Service
public class AirlinePriceBiz {
    @Autowired
    private ThirdUrlConfig thirdUrlConfig;
    @Autowired
    private MobileAPIRedisUtil mobileAPIRedisUtil;
    @Autowired
    private EtermService etermService;
    @Autowired
    private SPAAddonLineDao spaAddonLineDao;
    @Autowired
    private BasicDataService basicDataService;

    public void handAirlinePrice(String depCityCode, String arrCityCode, String flightDate) {
        PtQueryFlightFareResponse ptQueryFlightFareResponse = etermService.searchOne(depCityCode, arrCityCode, flightDate);
        if (ptQueryFlightFareResponse != null) {
            //价格缓存
            String key = StringUtil.envRedisDir(thirdUrlConfig.getRedisEnvDir()) + RedisKeyConstants.COMMON_FLIGHT_MIN_PRICE + ChannelCodeEnum.MOBILE.getChannelCode() + ":" + "MCNYOW" + flightDate + depCityCode + arrCityCode;
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptQueryFlightFareResponse.getResultCode())) {
                //如果没有低价缓存需要设置低价缓存
                //如果涉及SPA或者ADDON的国际航线同步记录mongo数据
                if(InterFlagEnum.I.getCode().equals(ptQueryFlightFareResponse.getInterFlag())){
                    List<SPAAddonLine> spaAddonLineList = spaAddonLineDao.query(depCityCode, arrCityCode, flightDate);
                    if (CollectionUtils.isEmpty(spaAddonLineList)) {
                        ConnectFlightCombInfoD connectD = new ConnectFlightCombInfoD();
                        connectD.setDepCity(depCityCode);
                        connectD.setArrCity(arrCityCode);
                        //查searchone接口
                        Map<String, AirPortInfoDTO> airPortInfoDTOMap = basicDataService.queryAirportMap();
                        List<ConnectFlightCombInfo> apiFlightCombInfoList = invokeSearchOne(ptQueryFlightFareResponse.getFlightInfoCombList(),airPortInfoDTOMap);
                        List<ConnectFlightCombInfoD> dayCombInfoDs = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(apiFlightCombInfoList)) {
                            connectD.setConnectFlightCombInfos(apiFlightCombInfoList);
                            connectD.setSource("API");
                            connectD.setType("SPA");
                            dayCombInfoDs.add(connectD);
                            //保存到MongoDB
                            recordSpaDataToMongo(dayCombInfoDs, flightDate, "SPA");
                        }
                    }
                }
            } else if (UnifiedOrderResultEnum.CABIN_NOT_ENOUGH.getResultCode().equals(ptQueryFlightFareResponse.getResultCode())) {
                log.info("handAirlinePrice:清理价格缓存:{}",key);
                mobileAPIRedisUtil.remove(key);
            }
        }
    }

    /**
     * searchone获取Addon数据
     * @param combApiList 航班组合数据
     * @return
     */
    private List<ConnectFlightCombInfo> invokeSearchOne(List<FlightInfoCombApi> combApiList,Map<String, AirPortInfoDTO> airPortInfoDTOMap) {
        if(CollectionUtils.isEmpty(combApiList)){
            return new ArrayList<>();
        }
        List<ConnectFlightCombInfo> connectFlightCombInfoList = Lists.newArrayList();
            ConnectFlightCombInfo connectFlightCombInfo;
            List<V2FlightInfo> flightInfoList;
            for (FlightInfoCombApi item : combApiList) {
                //航班明细结果
                flightInfoList = item.getFlightInfoList();
                connectFlightCombInfo = new ConnectFlightCombInfo();
                connectFlightCombInfo.setFlightDate(flightInfoList.get(0).getFlightDate());
                connectFlightCombInfo.setDepAirport(flightInfoList.get(0).getDepAirport());
                connectFlightCombInfo.setArrAirport(flightInfoList.get(flightInfoList.size() - 1).getArrAirport());
                //searchone接口有可能城市三字码返回的是机场三字码，需要转换
                String parseDepCity = null != airPortInfoDTOMap && null != airPortInfoDTOMap.get(flightInfoList.get(0).getDepCity()) ? airPortInfoDTOMap.get(flightInfoList.get(0).getDepCity()).getCityCode() : flightInfoList.get(0).getDepCity();
                String parseArrCity = null != airPortInfoDTOMap && null != airPortInfoDTOMap.get(flightInfoList.get(flightInfoList.size() - 1).getArrCity()) ? airPortInfoDTOMap.get(flightInfoList.get(flightInfoList.size() - 1).getArrCity()).getCityCode() : flightInfoList.get(flightInfoList.size() - 1).getArrCity();
                connectFlightCombInfo.setDepCity(parseDepCity);
                connectFlightCombInfo.setArrCity(parseArrCity);
                for (int i = 0; i < flightInfoList.size(); i++) {
                    connectFlightCombInfo.setStopAirport(convertStopCityAirport(flightInfoList, 1, airPortInfoDTOMap));
                    connectFlightCombInfo.setStopCity(convertStopCityAirport(flightInfoList, 0, airPortInfoDTOMap));
                    connectFlightCombInfo.setCombFlightNo(item.getFlightNoComb().replace("-", "+"));
                    connectFlightCombInfo.setConnectFlightInfoList(createCellFlightList(flightInfoList));
                }
                connectFlightCombInfoList.add(connectFlightCombInfo);
            }
        return connectFlightCombInfoList;
    }

    /**
     * searchone接口中转机场城市处理
     *
     * @param list
     * @param flag
     * @return
     */
    private String convertStopCityAirport(List<V2FlightInfo> list, int flag, Map<String, AirPortInfoDTO> airportData) {
        String stopCity = "";
        String airport = "";
        for (int i = 1; i < list.size(); i++) {
            if (i != 1) {
                stopCity = stopCity + "+";
                airport = airport + "+";
            }
            if (0 == flag) {
                stopCity += null != airportData && null != airportData.get(list.get(i).getDepCity()) ? airportData.get(list.get(i).getDepCity()).getCityCode() : list.get(i).getDepCity();
            }
            if (1 == flag) {
                airport += list.get(i).getDepAirport();
            }
        }
        return 0 == flag ? stopCity : 1 == flag ? airport : "";
    }

    /**
     * 拼装searchone联程航班舱位等信息
     *
     * @param list
     * @return
     */
    private List<ConnectFlightCombInfo.ConnectFlightInfo> createCellFlightList(List<V2FlightInfo> list) {
        List<ConnectFlightCombInfo.ConnectFlightInfo> resultList = list.stream().map(x -> {
            ConnectFlightCombInfo.ConnectFlightInfo cell = new ConnectFlightCombInfo.ConnectFlightInfo();
            cell.setFlightNo(x.getFlightNo());
            cell.setMealFlag(x.getMealCode());
            cell.setDepAirport(x.getDepAirport());
            cell.setDepCity(x.getDepCity());
            cell.setArrCity(x.getArrCity());
            cell.setArrAirport(x.getArrAirport());
            if (!Strings.isNullOrEmpty(x.getDepDateTime()) && x.getDepDateTime().length() > 14) {
                cell.setDepTime(x.getDepDateTime().substring(11).replace(":", ""));
                cell.setFlightDate(x.getDepDateTime().substring(0, 10));
            }
            if (!Strings.isNullOrEmpty(x.getArrDateTime()) && x.getArrDateTime().length() > 14) {
                cell.setArrTime(x.getArrDateTime().substring(11).replace(":", ""));
            }
            cell.setPlaneType(x.getFType());
            cell.setDepTerminal(x.getDepTerm());
            cell.setArrTerminal(x.getArrTerm());
            List<ConnectFlightCombInfo.ConnectFlightInfo.Cabin> cabins = Lists.newArrayList();
            ConnectFlightCombInfo.ConnectFlightInfo.Cabin cabin;
            if (!CollectionUtils.isEmpty(x.getCabinFareList())) {
                for (V2CabinFare cabinItem : x.getCabinFareList()) {
                    cabin = new ConnectFlightCombInfo.ConnectFlightInfo.Cabin(cabinItem.getCabinCode(), cabinItem.getCabinNumber());
                    cabins.add(cabin);
                }
                cell.setCabinList(cabins);
            }
            //SPA默认都是国际航班
            cell.setInterFlag("I");
            return cell;

        }).collect(Collectors.toList());
        return resultList;
    }

    /**
     * 保存数据
     *
     * @param list
     */
    private void recordSpaDataToMongo(List<ConnectFlightCombInfoD> list, String flightDate, String type) {
        if (CollectionUtils.isEmpty(list)) {
            log.info("抓取SPA航班任务结束；{}无{}航班数据", flightDate, type);
            return;
        }
        List<SPAAddonLine> spaAddonLineList = transSpaLine(list);
        spaAddonLineDao.removeDoc(spaAddonLineList,flightDate,type);
        spaAddonLineDao.addDocBatch(spaAddonLineList);
    }

    /**
     * 将连接航班组合信息列表转换为SPA附加航线列表
     * @param list 连接航班组合信息列表，包含多个连接航班组合信息对象
     * @return 转换后的SPA附加航线列表
     */
    private List<SPAAddonLine> transSpaLine(List<ConnectFlightCombInfoD> list) {
        List<SPAAddonLine> spa = new ArrayList<>();
        SPAAddonLine object;
        //航线组合遍历
        for (ConnectFlightCombInfoD cd : list) {
            //航班组合
            for (ConnectFlightCombInfo item : cd.getConnectFlightCombInfos()) {
                object = new SPAAddonLine();
                object.setFlightDate(item.getFlightDate());
                object.setDepAirport(item.getDepAirport());
                object.setArrAirport(item.getArrAirport());
                object.setStopAirport(item.getStopAirport());
                object.setDepCity(item.getDepCity());
                object.setArrCity(item.getArrCity());
                object.setStopCity(item.getStopCity());
                object.setCombFlightNo(item.getCombFlightNo());
                object.setConnectFlightInfos(JsonUtil.objectToJson(item.getConnectFlightInfoList()));
                object.setSource(cd.getSource());
                object.setType(cd.getType());
                object.setCreateTime(new Date());
                spa.add(object);
            }
        }
        return spa;
    }
}
