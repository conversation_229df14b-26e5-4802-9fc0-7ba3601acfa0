package com.juneyaoair.flightbasic.service.basic;

import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.request.airLine.*;
import com.juneyaoair.flightbasic.response.airline.FollowAirLineResDTO;

import java.util.List;

/**
 * @program: flightbasic
 * @description
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2019-07-16 18:57
 **/
public interface FollowAirLineService {
    /**
     * 查询关注航班列表
     * @param reqDTO
     * @return
     */

    BaseResultDTO<List<FollowAirLineResDTO>> queryFollowAirLine(FollowAirLineReqDTO reqDTO);

    /**
     * 查询关注航班列表
     * @param ffpId
     * @param ffpCardNo
     * @return
     */
    List<FollowAirLineResDTO> queryFollowAirLine(String ffpId,String ffpCardNo);
    /**
     * 取消航班关注
     * @param reqDTO
     * @return
     */
    int cancelAirline(AddFollowAirlineReqDTO reqDTO);
    /**
     * 取消航班关注
     *
     * @param cancelAttentionFlightParam
     * @return
     */
    void cancelAirline(String ffpId, String ffpCardNo, CancelAttentionFlightParam cancelAttentionFlightParam);

    /**
     * 根据ID查询关注航班详情
     * @param ffpId
     * @param ffpCardNo
     * @param queryAttentionFlightParam
     * @return
     */
    FollowAirLineResDTO queryAttentionDetail(String ffpId, String ffpCardNo, QueryAttentionFlightParam queryAttentionFlightParam);


    /**
     * 新增航班关注
     * @param reqDTO
     * @return
     */

    int addAirline(AddFollowAirlineReqDTO reqDTO);
    /**
     * 新增航班关注或更新航班关注
     * @param attentionFlightParam
     * @return
     */
    String addorUpdateAirline(String ffpId,String ffpCardNo,AttentionFlightParam attentionFlightParam);
}
