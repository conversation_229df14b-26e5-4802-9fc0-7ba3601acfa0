package com.juneyaoair.flightbasic.dao.basic;

import com.juneyaoair.flightbasic.model.basic.FollowAirLinePO;

import java.util.List;
import java.util.Set;

/**
 * @program: flightbasic
 * @description
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2019-07-16 19:02
 **/
public interface FollowAirLineDao {

    List<FollowAirLinePO> queryFollowAirLine(FollowAirLinePO reqPO);
    List<FollowAirLinePO> queryFollowAirLine(String ffpId,String ffpCardNo);

    //查询是否关注过该航班
    FollowAirLinePO queryFollowAirLineOne(FollowAirLinePO reqPO);
    //更新操作
    int updateAirline(FollowAirLinePO reqDTO);
    int updateAirlineById(FollowAirLinePO reqDTO);

    int addAirline(FollowAirLinePO reqDTO);

    int cancelAirline(FollowAirLinePO reqDTO);

    /**
     * 取消航班关注
     * @param followAirLinePO
     * @return
     */
    int cancelConcern(FollowAirLinePO followAirLinePO);

    /**
     * 查询指定航班关注用户清单
     * @param flightNo
     * @param flightDate
     * @param depAirportCode
     * @param arrAirportCode
     * @return
     */
    Set<String> getFlightConcernFfp(String flightNo, String flightDate, String depAirportCode, String arrAirportCode);
}
