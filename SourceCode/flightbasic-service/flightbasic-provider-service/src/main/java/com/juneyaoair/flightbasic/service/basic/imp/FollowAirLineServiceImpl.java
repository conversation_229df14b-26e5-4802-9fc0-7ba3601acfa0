package com.juneyaoair.flightbasic.service.basic.imp;

import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.dao.basic.FollowAirLineDao;
import com.juneyaoair.flightbasic.exception.ArgumentCheckFailException;
import com.juneyaoair.flightbasic.exception.ServiceException;
import com.juneyaoair.flightbasic.external.service.BigDataOdsService;
import com.juneyaoair.flightbasic.mapstruct.FollowAirLineMapstruct;
import com.juneyaoair.flightbasic.model.basic.FollowAirLinePO;
import com.juneyaoair.flightbasic.request.airLine.*;
import com.juneyaoair.flightbasic.response.airline.FollowAirLineResDTO;
import com.juneyaoair.flightbasic.service.basic.FlightInfoService;
import com.juneyaoair.flightbasic.service.basic.FollowAirLineService;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @program: flightbasic
 * @description
 * @author: zhangwanli
 * @create: 2019-07-16 18:58
 **/
@Service
@Slf4j
public class FollowAirLineServiceImpl implements FollowAirLineService {

    @Resource
    private FollowAirLineDao followAirLineDao;
    @Autowired
    private BigDataOdsService bigDataOdsService;
    @Autowired
    private FlightInfoService flightInfoService;

    @Override
    public BaseResultDTO<List<FollowAirLineResDTO>> queryFollowAirLine(FollowAirLineReqDTO reqDTO) {
        BaseResultDTO<List<FollowAirLineResDTO>> result = new BaseResultDTO<>();
        FollowAirLinePO followAirLinePO = new FollowAirLinePO();
        try {
            BeanUtils.copyNotNullProperties(reqDTO, followAirLinePO);
            List<FollowAirLinePO> followAirLinePOS = followAirLineDao.queryFollowAirLine(followAirLinePO);
            List<FollowAirLineResDTO> DTOList = new ArrayList<>();
            FollowAirLineResDTO followAirLineResDTO;
            for (FollowAirLinePO airLinePO : followAirLinePOS) {
                followAirLineResDTO = new FollowAirLineResDTO();
                BeanUtils.copyNotNullProperties(airLinePO, followAirLineResDTO);
                DTOList.add(followAirLineResDTO);
            }
            result.setResult(DTOList);
        } catch (Exception e) {
            log.error("方法：{}查询关注航班信息错误!{}参数信息{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e, reqDTO);
        }
        return result;
    }

    @Override
    public List<FollowAirLineResDTO> queryFollowAirLine(String ffpId, String ffpCardNo) {
        if (StringUtils.isBlank(ffpCardNo)) {
            throw new ArgumentCheckFailException("会员信息不可为空");
        }
        List<FollowAirLinePO> followAirLinePOS = followAirLineDao.queryFollowAirLine(ffpId, ffpCardNo);
        List<FollowAirLineResDTO> followAirLineResDTOList;
        if (CollectionUtils.isNotEmpty(followAirLinePOS)) {
            followAirLineResDTOList = FollowAirLineMapstruct.MAPPER.toFollowAirLineResDTOList(followAirLinePOS);
        } else {
            followAirLineResDTOList = new ArrayList<>();
        }
        return followAirLineResDTOList;
    }

    @Override
    public int cancelAirline(AddFollowAirlineReqDTO reqDTO) {
        FollowAirLinePO followAirLinePO = new FollowAirLinePO();
        int i = 0;
        try {
            BeanUtils.copyNotNullProperties(reqDTO, followAirLinePO);
            followAirLinePO.setLastModifyTime(DateUtils.toTotalDate(DateUtils.getCurrentDateTimesStr()));
            i = followAirLineDao.cancelAirline(followAirLinePO);
        } catch (Exception e) {
            log.error("方法：{}取消关注航班信息错误!{} 参数信息{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e, reqDTO);
        }
        return i;
    }

    @Override
    public void cancelAirline(String ffpId, String ffpCardNo, CancelAttentionFlightParam cancelAttentionFlightParam) {
        if (StringUtils.isBlank(cancelAttentionFlightParam.getId())) {
            throw new ArgumentCheckFailException("记录编号不可为空");
        }
        if (StringUtils.isAnyBlank(ffpId, ffpCardNo)) {
            throw new ArgumentCheckFailException("会员信息不可为空");
        }
        FollowAirLinePO followAirLinePO = new FollowAirLinePO();
        followAirLinePO.setFfpId(ffpId);
        followAirLinePO.setFfpCardNo(ffpCardNo);
        followAirLinePO.setId(cancelAttentionFlightParam.getId());
        if (followAirLineDao.cancelConcern(followAirLinePO) < 1) {
            throw new ServiceException("取消关注航班失败");
        }
    }

    @Override
    public FollowAirLineResDTO queryAttentionDetail(String ffpId, String ffpCardNo, QueryAttentionFlightParam queryAttentionFlightParam) {
        if (StringUtils.isBlank(queryAttentionFlightParam.getId())) {
            throw new ArgumentCheckFailException("记录编号不可为空");
        }
        if (StringUtils.isAnyBlank(ffpId, ffpCardNo)) {
            throw new ArgumentCheckFailException("会员信息不可为空");
        }
        FollowAirLinePO followAirLinePOParam = new FollowAirLinePO();
        followAirLinePOParam.setFfpId(ffpId);
        followAirLinePOParam.setFfpCardNo(ffpCardNo);
        followAirLinePOParam.setId(queryAttentionFlightParam.getId());
        FollowAirLinePO followAirLinePO = followAirLineDao.queryFollowAirLineOne(followAirLinePOParam);
        if(followAirLinePO!=null){
            FollowAirLineResDTO followAirLineResDTO = FollowAirLineMapstruct.MAPPER.toFollowAirLineResDTO(followAirLinePO);
            return followAirLineResDTO;
        }
        return null;
    }

    @Override
    public int addAirline(AddFollowAirlineReqDTO reqDTO) {
        FollowAirLinePO followAirLinePO = new FollowAirLinePO();
        FollowAirLinePO followPO = new FollowAirLinePO();
        int i = 0;
        try {
            BeanUtils.copyNotNullProperties(reqDTO, followPO);
            if (!StringUtil.isNullOrEmpty(reqDTO.getArrAirportCode()) || !StringUtil.isNullOrEmpty(reqDTO.getDepAirportCode())) {
                reqDTO.setArrAirportCode(null);
                reqDTO.setDepAirportCode(null);
            }
            BeanUtils.copyNotNullProperties(reqDTO, followAirLinePO);
            FollowAirLinePO response = followAirLineDao.queryFollowAirLineOne(followAirLinePO);
            //最后修改时间
            followPO.setLastModifyTime(DateUtils.toTotalDate(DateUtils.getCurrentDateTimesStr()));
            followPO.setConcern("1");
            if (null != response) {
                i = followAirLineDao.updateAirline(followPO);
            } else {
                followAirLinePO.setCreateTime(DateUtils.toTotalDate(DateUtils.getCurrentDateTimesStr()));
                i = followAirLineDao.addAirline(followPO);
            }
        } catch (DuplicateKeyException e) {
            log.info("方法：{}增加关注航班信息重复!{},参数信息{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e, reqDTO);
            return 1;
        } catch (Exception e) {
            log.error("方法：{}增加关注航班信息错误!{},参数信息{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e, reqDTO);
        }
        return i;
    }

    @Override
    public String addorUpdateAirline(String ffpId, String ffpCardNo, AttentionFlightParam attentionFlightParam) {
        if (StringUtils.isAnyBlank(ffpId, ffpCardNo)) {
            throw new ArgumentCheckFailException("会员信息不可为空");
        }
        FollowAirLinePO followAirLinePO = FollowAirLineMapstruct.MAPPER.toFollowAirLinePO(attentionFlightParam);
        followAirLinePO.setFfpId(ffpId);
        followAirLinePO.setFfpCardNo(ffpCardNo);
        FollowAirLinePO oldFollowAirLinePO = followAirLineDao.queryFollowAirLineOne(followAirLinePO);
        if (Objects.isNull(oldFollowAirLinePO)) {
            if (followAirLineDao.addAirline(followAirLinePO) < 1) {
                throw new ServiceException("新增关注航班失败");
            }
            return followAirLinePO.getId();
        }else{
            followAirLinePO.setId(oldFollowAirLinePO.getId());
            followAirLineDao.updateAirlineById(followAirLinePO);
            return oldFollowAirLinePO.getId();
        }
    }
}
