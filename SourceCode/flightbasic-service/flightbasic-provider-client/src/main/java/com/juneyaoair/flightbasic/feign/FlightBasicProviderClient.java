package com.juneyaoair.flightbasic.feign;

import com.github.pagehelper.PageInfo;
import com.juneyaoair.flightbasic.advertisement.AdvertisementDto;
import com.juneyaoair.flightbasic.advertisement.AdvertisementParam;
import com.juneyaoair.flightbasic.aircraft.AircraftTypeInfo;
import com.juneyaoair.flightbasic.aircraft.AircraftTypeInfoQuery;
import com.juneyaoair.flightbasic.api.index.*;
import com.juneyaoair.flightbasic.api.index.cobrandcreditcard.CoBrandCreditCardAdBO;
import com.juneyaoair.flightbasic.ce.InventoryHistoryRQ;
import com.juneyaoair.flightbasic.common.*;
import com.juneyaoair.flightbasic.commondto.RequestData;
import com.juneyaoair.flightbasic.commondto.ResponseData;
import com.juneyaoair.flightbasic.distance.request.FlightDistanceReq;
import com.juneyaoair.flightbasic.dto.FlightDistanceDTO;
import com.juneyaoair.flightbasic.fallback.FlightBasicProviderFallbackFactory;
import com.juneyaoair.flightbasic.i18ndict.I18nDictionaryParam;
import com.juneyaoair.flightbasic.i18ndict.I18nDictionaryRequestDTO;
import com.juneyaoair.flightbasic.mall.CommonRecommendTravel;
import com.juneyaoair.flightbasic.panasonic.FocFlightParam;
import com.juneyaoair.flightbasic.request.airLine.*;
import com.juneyaoair.flightbasic.request.country.TCountryReqDTO;
import com.juneyaoair.flightbasic.request.flightInfo.FlightExistDateReqDTO;
import com.juneyaoair.flightbasic.request.flightInfo.FlightInfoReqDTO;
import com.juneyaoair.flightbasic.request.notice.NoticeRequestDTO;
import com.juneyaoair.flightbasic.request.notice.request.Message;
import com.juneyaoair.flightbasic.request.notice.request.NewsRequest;
import com.juneyaoair.flightbasic.request.notice.resposne.NewsDetailResponse;
import com.juneyaoair.flightbasic.request.notice.resposne.NewsResponse;
import com.juneyaoair.flightbasic.request.notice.resposne.NoticeResponse;
import com.juneyaoair.flightbasic.request.payMethod.ParamPayMethodDTO;
import com.juneyaoair.flightbasic.request.push.PushDetailQuery;
import com.juneyaoair.flightbasic.request.push.PushDetailUpdateState;
import com.juneyaoair.flightbasic.request.wechat.SpecialAirlineDTO;
import com.juneyaoair.flightbasic.response.HolidayCalenderResponse;
import com.juneyaoair.flightbasic.response.airline.FollowAirLineResDTO;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.flightbasic.response.country.TCountryDTO;
import com.juneyaoair.flightbasic.response.flightInfo.FlightExistDateDTO;
import com.juneyaoair.flightbasic.response.flightInfo.FlightInfoDTO;
import com.juneyaoair.flightbasic.response.notice.NoticeInfoResponseDTO;
import com.juneyaoair.flightbasic.response.notice.TNoticeInfoResponseDTO;
import com.juneyaoair.flightbasic.response.paynote.PayMethodDTO;
import com.juneyaoair.flightbasic.theme.ThemeSwitchDTO;
import com.juneyaoair.flightbasic.theme.ThemeSwitchRequestDTO;
import com.juneyaoair.flightbasic.time.TimeZoneParam;
import com.juneyaoair.flightbasic.time.TimeZoneResult;
import com.juneyaoair.flightbasic.travelreminder.TravelReminderResponse;
import com.juneyaoair.flightbasic.version.request.VersionReq;
import com.juneyaoair.flightbasic.version.response.VersionDto;
import feign.Request;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(name = "flightbasic-provider-service", fallbackFactory = FlightBasicProviderFallbackFactory.class, contextId = "FlightBasicProviderApi")
public interface FlightBasicProviderClient {
    /**
     * 行距查询
     * @param baseRequestDTO
     * @return
     */
    @PostMapping("/basic/flightDistance/query")
    BaseResultDTO<FlightDistanceDTO> queryDistance(BaseRequestDTO<FlightDistanceReq> baseRequestDTO);
    /**
     * 查询数据版本信息
     * @param versionReq
     * @return
     */
    @PostMapping("/indexHandle/getDataVersionList")
    BaseResponseDTO<List<VersionDto>> getDataVersionList(VersionReq versionReq);

    /**
     * 城市信息查询
     * @param requestDTO
     * @return
     */
    @PostMapping("/api/queryCity")
    BaseResultDTO<List<ApiCityInfoDto>> queryCity(BaseRequestDTO requestDTO);

    /**
     * 查询机场信息
     * @param requestDTO
     * @return
     */
    @PostMapping("/api/queryAirports")
    BaseResultDTO<List<ApiAirPortInfoDto>> queryAllAirports(BaseRequestDTO requestDTO);

    /**
     * 支付方式查询
     * @param param
     * @return
     */
    @PostMapping("/payMethod/searchPayMethods")
    BaseResultDTO<List<PayMethodDTO>> searchPayMethods(BaseRequestDTO<ParamPayMethodDTO> param);

    /**
     * 运营报告，责任报告分页查询
     * @param baseReq
     * @return
     */
    @PostMapping("/advertisement/pageList")
    BaseRespDTO<PageInfo<AdvertisementDto>> pageList(BaseReq<AdvertisementParam> baseReq);

    /**
     * 查询默认范围内是否有航班计划
     * @param requestDTO
     * @return
     */
    @PostMapping("/basic/existFlightByCityAndDate")
    BaseResultDTO<List<FlightExistDateDTO>> existFlightByCityAndDate(BaseRequestDTO<FlightExistDateReqDTO> requestDTO) ;

    /**
     * 特价航线查询
     * @param baseReq
     * @return
     */
    @PostMapping("/indexHandle/getSpecialAirLineListV2")
    BaseResultDTO<List<SpecialAirlineDTO>> getSpecialAirLinesV2(BaseReq baseReq);

    /**
     * 根据模块编码获取分类信息
     * @param req
     * @return
     */
    @PostMapping("/indexHandle/getAllNoticeInfo")
    BaseResponseDTO<List<NoticeInfoResponseDTO>> getAllNoticeInfo(BaseReq<NoticeRequestDTO> req);

    /**
     * 根据ID获取指定条款信息
     * @param req
     * @return
     */
    @PostMapping(value = "/indexHandle/getRichTextNoticeInfo")
    BaseResponseDTO<TNoticeInfoResponseDTO> getRichTextNoticeInfo(BaseReq<NoticeRequestDTO> req) ;


    /**
     * 分页获取公告
     */
    @PostMapping("/indexHandle/queryMessageByPage")
    @ApiOperation(value = "获取APP公告列表3.0", notes = "获取APP公告列表3.0")
    BaseResultDTO<PageInfo<NoticeResponse>> queryMessageByPage(Message message);

    @PostMapping("/indexHandle/getSaleOffice")
    @ApiOperation(value = "获取营业部info", notes = "获取营业部info")
    BaseResultDTO<SaleOfficeResp> getSaleOffice(SaleOfficeReq req);

    /**
     * 查询关注航班
     * @param requestData
     * @return
     */
    @PostMapping("/attentionFlight/queryAttentionFlightList")
    ResponseData<List<FollowAirLineResDTO>> queryAttentionFlightList(RequestData requestData);

    /**
     * 增加航班关注
     * @param requestData
     * @return
     */
    @PostMapping("/attentionFlight/addAttentionFlight")
    ResponseData addAttentionFlight(RequestData<AttentionFlightParam> requestData);

    /**
     * 取消航班关注
     * @param requestData
     * @return
     */
    @PostMapping("/attentionFlight/cancelAttentionFlight")
    ResponseData cancelAttentionFlight(RequestData<CancelAttentionFlightParam> requestData);

    /**
     * 航班关注ID详情查询
     * @param requestData
     * @return
     */
    @PostMapping("/attentionFlight/queryAttentionDetail")
    ResponseData<FollowAirLineResDTO> queryAttentionDetail(RequestData<QueryAttentionFlightParam> requestData);

    /**
     * 国家信息查询
     * @param
     * @return
     */
    @PostMapping("/basic/countrysV2")
    ResponseData<List<TCountryDTO>> countryInfoV2(RequestData<TCountryReqDTO> requestDTO);

    /**
     * 获取指定消息记录清单
     * @param requestBase
     * @return
     */
    @PostMapping(value = "/pushDetail/getPushDetailList")
    BaseResultDTO<List<Mess>> getPushDetailList(@RequestBody @Validated BaseRequestDTO<PushDetailQuery> requestBase);

    /**
     * 获取未读消息的数量
     * @param requestBase
     * @return
     */
    @PostMapping(value = "/pushDetail/getNoReadNum")
    BaseResultDTO<Long> getNoReadNum(@RequestBody @Validated BaseRequestDTO<PushDetailQuery> requestBase);

    /**
     * 更新消息状态
     * @param requestBase
     * @return
     */
    @PostMapping(value = "/pushDetail/updateState")
    BaseResultDTO<Object> updateState(@RequestBody @Validated BaseRequestDTO<PushDetailUpdateState> requestBase);

    /**
     * 分页获取吉祥新闻
     */
    @PostMapping("/indexHandle/toCatchJXNews")
    @ApiOperation(value = "获取吉祥新闻", notes = "获取吉祥新闻")
    BaseResultDTO<PageInfo<NewsResponse>> toCatchJXNews(NewsRequest newsRequest);

    /**
     * 获取吉祥新闻详情
     */
    @PostMapping("/indexHandle/toCatchJXNewsDetail")
    @ApiOperation(value = "获取吉祥新闻详情", notes = "获取吉祥新闻详情")
    BaseResultDTO<NewsDetailResponse> toCatchJXNewsDetail(NewsRequest newsRequest);

    @PostMapping("/indexHandle/getWechatUnlimitedQRCode")
    @ApiOperation(value = "获取不限制的微信小程序码", notes = "获取不限制的微信小程序码")
    ResponseData<GetWechatQRCodeResp> getWechatUnlimitedQRCode(@RequestBody RequestData<GetWechatQRCodeReq> req);

    /**
     * @return com.juneyaoair.flightbasic.common.BaseResultDTO<com.juneyaoair.flightbasic.response.HolidayCalenderResponse>
     * <AUTHOR>
     * @Description 获取节假日列表信息
     * @Date 13:11 2024/3/8
     **/
    @GetMapping("/cityLowPrice/toCatchHolidayCalender")
    @ApiOperation(value = "获取节假日列表信息", notes = "获取节假日列表信息")
    BaseResultDTO<HolidayCalenderResponse> toCatchHolidayCalender();

    @ApiOperation(value = "查询机型信息",notes = "查询机型信息")
    @PostMapping(value = "/aircraftTypeInfo/getAircraftTypeInfo")
    BaseResultDTO<List<AircraftTypeInfo>> getAircraftTypeInfo(BaseRequestDTO<AircraftTypeInfoQuery> baseRequest);
    
    @ApiOperation(value = "查询所有联名信用卡广告信息",notes = "查询所有联名信用卡广告信息")
    @PostMapping(value = "/indexHandle/coBrandCreditCardAd")
    BaseResultDTO<List<CoBrandCreditCardAdBO>> getCoBrandCreditCardAd(BaseRequestDTO baseRequestDTO);

    @ApiOperation(value = "查询i18n字典", notes = "数据源I18N_DICTIONARY/I18N_DICTIONARY_VALUE")
    @PostMapping(value = "/i18nDictionary/list")
    BaseResultDTO<Map<String, Map<String, Map<String, String>>>> getI18nDictionary(BaseRequestDTO<I18nDictionaryRequestDTO> baseRequestDTO);

    @ApiOperation(value = "国际化语言字典（支持缓存）", httpMethod = "POST")
    @PostMapping(value = "/i18nDictionary/getValue")
    ResponseData<Map<String, Map<String, String>>> getList(@RequestBody @Valid RequestData<I18nDictionaryParam> request);

    @ApiOperation(value = "获取机票预订出行提醒信息", notes = "获取所有有效的出行提醒信息")
    @PostMapping(value = "/indexHandle/getTravelReminder")
    ResponseData<List<TravelReminderResponse>> getTravelReminder(@RequestBody RequestData<Void> request);

    /**
     * 获取主题开关配置
     * @param requestDTO 请求参数
     * @return 主题开关配置
     */
    @PostMapping("/theme/getThemeSwitch")
    @ApiOperation(value = "获取主题开关配置")
    ResponseData<ThemeSwitchDTO> getThemeSwitch(@RequestBody RequestData<ThemeSwitchRequestDTO> requestDTO);

    @PostMapping("/ceRouting/callInventoryHistory")
    @ApiOperation(value = "DIH数据下载")
    ResponseData callInventoryHistory(@RequestParam(required = false,name = "options") Request.Options options, @RequestBody RequestData<InventoryHistoryRQ> requestData);

    @PostMapping("/sendPanasonicMsg")
    @ApiOperation(value = "松下报文发送")
    ResponseData sendPanasonicMsg(@RequestBody RequestData<FocFlightParam> requestData);

    @ApiOperation(value = "查询航班信息", notes = "根据出发、到达机场或城市等信息查询，出发到达机场为必须字段")
    @PostMapping(value = "/basic/flightInfoList")
    BaseResultDTO<List<FlightInfoDTO>> searchFlightInfoList(@RequestBody BaseRequestDTO<FlightInfoReqDTO> requestDTO);

    /**
     * 查询航线简要信息
     * @param requestDTO 请求参数
     * @return 返回压缩后的航线数据
     */
    @ApiOperation(value = "查询航线简要信息", notes = "航线简要查询,实时获取无缓存，不适合高并发使用")
    @PostMapping("/basic/queryAirlineSimple")
    ResponseData<String> queryAirlineSimple(@RequestBody RequestData<AirLineReqDTO> requestDTO) ;

    @ApiOperation(value = "新版积分商城商品查询")
    @PostMapping("/mallShopProduct/queryHotelProductsV2")
    ResponseData<List<CommonRecommendTravel>> queryHotelProductsV2();

    @ApiOperation(value = "检查与添加航线",notes = "实时获取无缓存，不适合高并发使用")
    @PostMapping(value = "/airline/checkAndAddAirline")
    ResponseData checkAndAddAirline(@RequestBody RequestData<AirlineParam> requestData);

    /**
     * 对时间进行时区转换
     * @param baseRequest
     * @return
     */
    @PostMapping("/timeZone/formatTimeZone")
    ResponseData<TimeZoneResult> formatTimeZone(@RequestBody @Validated RequestData<TimeZoneParam> baseRequest);

    /**
     * 根据S1价格处理航线以及价格缓存
     * @return
     */
    @PostMapping(value = "/airline/handAirlinePrice",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseData handAirlinePrice(@RequestBody @Validated RequestData<AirlinePriceParam> requestData) ;

}
