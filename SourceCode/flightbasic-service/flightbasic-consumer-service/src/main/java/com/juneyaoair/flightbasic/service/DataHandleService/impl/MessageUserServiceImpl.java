package com.juneyaoair.flightbasic.service.DataHandleService.impl;



import com.juneyaoair.flightbasic.request.wechat.MessageUserDTO;
import com.juneyaoair.flightbasic.feign.api.FeignMessageUserService;
import com.juneyaoair.flightbasic.service.DataHandleService.IMessageUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by lzg on 2016-05-18.
 */
@Service("messageUserService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class MessageUserServiceImpl implements IMessageUserService {
    private Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private FeignMessageUserService feignMessageUserService;


    @Override
    public int deleteMessageUser(MessageUserDTO[] messageUsers) {
        try {
                feignMessageUserService.deleteMessageUser(messageUsers);
        }catch (Exception e){
            log.debug("删除出错");
            return 0;
        }
        return 1;
    }
}
