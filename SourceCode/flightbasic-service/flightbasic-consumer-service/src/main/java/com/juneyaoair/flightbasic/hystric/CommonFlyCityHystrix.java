package com.juneyaoair.flightbasic.hystric;

import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.feign.basic.FeignCommonflyCityService;
import com.juneyaoair.flightbasic.request.commonFlyCity.CommonFlyCityReqDTO;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class CommonFlyCityHystrix implements FeignCommonflyCityService {
    @Override
    public Map queryCommonFlyCityDetailsByCityCode(CommonFlyCityReqDTO requestDTO) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("resultCode", WSEnum.SERVICE_FUSING.resultCode);
        resultMap.put("resultMsg", WSEnum.SERVICE_FUSING.resultInfo);
        return resultMap;
    }
}
