package com.juneyaoair.flightbasic.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/17  20:19.
 */
@Data
@Component
public class CrmConfig {
    @Value("${crm.api.url}")
    private String apiUrl;
    @Value("${crm.channelCode}")
    private String channelCode;
    @Value("${crm.clientSecret}")
    private String clientSecret;
}
