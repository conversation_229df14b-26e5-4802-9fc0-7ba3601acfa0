package com.juneyaoair.flightbasic.hystric;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.feign.basic.FeignCityInfoService;
import com.juneyaoair.flightbasic.request.city.CityInfoReqDTO;
import com.juneyaoair.flightbasic.response.city.CityInfoDTO;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public class CityInfoHystrix implements FeignCityInfoService {
    @Override
    public BaseResultDTO<List<com.juneyaoair.flightbasic.response.city.CityInfoDTO>> getAllCityInfos(BaseRequestDTO<CityInfoReqDTO> requestDTO) {
        BaseResultDTO<List<CityInfoDTO>> baseResultDTO = new BaseResultDTO();
        baseResultDTO.setResultCode(WSEnum.SERVICE_FUSING.resultCode);
        baseResultDTO.setErrorMsg(WSEnum.SERVICE_FUSING.resultInfo);
        return baseResultDTO;
    }

    @Override
    public BaseResultDTO<List<CityInfoDTO>> getAllCityInfosV2(BaseRequestDTO<CityInfoReqDTO> requestDTO) {
        BaseResultDTO<List<CityInfoDTO>> baseResultDTO = new BaseResultDTO();
        baseResultDTO.setResultCode(WSEnum.SERVICE_FUSING.resultCode);
        baseResultDTO.setErrorMsg(WSEnum.SERVICE_FUSING.resultInfo);
        return baseResultDTO;
    }
}
