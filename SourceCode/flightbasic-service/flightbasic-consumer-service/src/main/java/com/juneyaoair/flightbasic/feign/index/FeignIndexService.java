package com.juneyaoair.flightbasic.feign.index;

import com.juneyaoair.flightbasic.common.BaseResponseDTO;
import com.juneyaoair.flightbasic.hystric.IndexHystrix;
import com.juneyaoair.flightbasic.request.wechat.PictureDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = "flightbasic-provider-service",fallback = IndexHystrix.class, contextId = "FeignIndexService")
public interface FeignIndexService {

    @RequestMapping(value = "indexHandle/getPictureList")
    BaseResponseDTO getPictureList(PictureDTO pic);

}
