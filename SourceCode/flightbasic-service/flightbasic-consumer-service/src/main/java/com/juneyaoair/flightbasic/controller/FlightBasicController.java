package com.juneyaoair.flightbasic.controller;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.request.airLine.AirLineReqDTO;
import com.juneyaoair.flightbasic.request.airport.AirPortInfoReqDTO;
import com.juneyaoair.flightbasic.request.city.CityInfoReqDTO;
import com.juneyaoair.flightbasic.request.commonFlyCity.CommonFlyCityReqDTO;
import com.juneyaoair.flightbasic.request.cooperationAirLine.CooperationAirlineReqDTO;
import com.juneyaoair.flightbasic.request.country.TCountryReqDTO;
import com.juneyaoair.flightbasic.request.flightInfo.FlightInfoReqDTO;
import com.juneyaoair.flightbasic.request.flightInfo.ThemeFlightParam;
import com.juneyaoair.flightbasic.request.flightTicketPrice.FlightTicketPriceByYDTO;
import com.juneyaoair.flightbasic.request.province.ProvinceReqDTO;
import com.juneyaoair.flightbasic.request.trrFlightInfo.ParamTrrFlightInfo;
import com.juneyaoair.flightbasic.response.airline.AirLineDTO;
import com.juneyaoair.flightbasic.response.airline.ThemeAirline;
import com.juneyaoair.flightbasic.response.airport.AirPortInfoDTO;
import com.juneyaoair.flightbasic.response.city.CityInfoDTO;
import com.juneyaoair.flightbasic.response.cooperationAirLine.CooperationAirlineDTO;
import com.juneyaoair.flightbasic.response.country.TCountryDTO;
import com.juneyaoair.flightbasic.response.flightInfo.FlightInfoDTO;
import com.juneyaoair.flightbasic.response.flightPrice.FlightTicketPriceByYResp;
import com.juneyaoair.flightbasic.response.province.ProvinceDTO;
import com.juneyaoair.flightbasic.response.trrFlightInfo.TrrFlightInfoDTO;
import com.juneyaoair.flightbasic.service.basic.FlightBasicService;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.ObjCheckUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/basic")
@Slf4j
@Api(value = "FlightBasicController",tags = "航班基础信息服务api")
public class FlightBasicController {
    @Autowired
    private FlightBasicService flightBasicService;

    @ApiOperation(value = "查询国家信息", notes = "根据国家三字码、国家名等信息查询国家信息")
    @PostMapping("/countrys")
    public BaseResultDTO<List<TCountryDTO>> countryInfo(@RequestBody BaseRequestDTO<TCountryReqDTO> requestDTO, HttpServletRequest request, BindingResult validResult) {
        log.info("【接口：{}，请求参数：{}】", request.getRequestURI(), JsonUtil.objectToJson(requestDTO));
        BaseResultDTO<List<TCountryDTO>> resultDTO = new BaseResultDTO<>();
        BeanUtils.copyNotNullProperties(requestDTO,resultDTO);
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultDTO.setErrorMsg(strMess);
            resultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return resultDTO;
        }
        try {
            resultDTO = flightBasicService.searchCountry(requestDTO);
        } catch (Exception e) {
            log.error("方法{},发生异常!{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;
    }

    @ApiOperation(value = "查询省份信息", notes = "根据城市码等信息查询城市信息")
    @PostMapping(value = "provinces")
    public BaseResultDTO<List<ProvinceDTO>> getAllProvince(@RequestBody BaseRequestDTO<ProvinceReqDTO> requestDTO, HttpServletRequest request, BindingResult validResult) {
        log.info("【接口：{}，请求参数：{}】", request.getRequestURI(), JsonUtil.objectToJson(requestDTO));
        BaseResultDTO<List<ProvinceDTO>> resultDTO = new BaseResultDTO<>();
        BeanUtils.copyNotNullProperties(requestDTO,resultDTO);
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultDTO.setErrorMsg(strMess);
            resultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return resultDTO;
        }
        try {
            resultDTO = flightBasicService.searchProvince(requestDTO);
        } catch (Exception e) {
            log.error("方法{},发生异常!{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;
    }

    @ApiOperation(value = "查询城市信息", notes = "根据城市码等信息查询城市信息")
    @PostMapping(value = "citys")
    public BaseResultDTO<List<CityInfoDTO>> getAllCityInfos(@RequestBody BaseRequestDTO<CityInfoReqDTO> requestDTO, HttpServletRequest request, BindingResult validResult) {
        log.info("【接口：{}，请求参数：{}】", request.getRequestURI(), JsonUtil.objectToJson(requestDTO));
        BaseResultDTO<List<CityInfoDTO>> resultDTO = new BaseResultDTO<>();
        BeanUtils.copyNotNullProperties(requestDTO,resultDTO);
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultDTO.setErrorMsg(strMess);
            resultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return resultDTO;
        }
        try {
            resultDTO = flightBasicService.searchCityInfo(requestDTO);
        } catch (Exception e) {
            log.error("方法{},发生异常!{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;
    }

    @ApiOperation(value = "查询城市信息V2", notes = "根据城市码等信息查询城市信息")
    @PostMapping(value = "citysV2")
    public BaseResultDTO<List<CityInfoDTO>> getAllCityInfosV2(@RequestBody BaseRequestDTO<CityInfoReqDTO> requestDTO, BindingResult validResult,HttpServletRequest request) {
        log.info("【接口：{}，请求参数：{}】", request.getRequestURI(), JsonUtil.objectToJson(requestDTO));
        BaseResultDTO<List<CityInfoDTO>> resultDTO = new BaseResultDTO<>();
        BeanUtils.copyNotNullProperties(requestDTO,resultDTO);
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultDTO.setErrorMsg(strMess);
            resultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return resultDTO;
        }
        try {
            resultDTO = flightBasicService.searchCityInfoV2(requestDTO);
        } catch (Exception e) {
            log.error("方法{},发生异常!{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;
    }

    @ApiOperation(value = "查询机场信息", notes = "根据机场码等信息查询机场信息")
    @PostMapping(value = "airPortInfos")
    @Deprecated
    public BaseResultDTO<List<AirPortInfoDTO>> getAllAirPortInfo(@RequestBody BaseRequestDTO<AirPortInfoReqDTO> requestDTO,BindingResult validResult, HttpServletRequest request) {
        log.info("【接口：{}，请求参数：{}】", request.getRequestURI(), JsonUtil.objectToJson(requestDTO));
        BaseResultDTO<List<AirPortInfoDTO>> resultDTO = new BaseResultDTO<>();
        BeanUtils.copyNotNullProperties(requestDTO,resultDTO);
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultDTO.setErrorMsg(strMess);
            resultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return resultDTO;
        }
        try {
            resultDTO = flightBasicService.searchAirport(requestDTO);
        } catch (Exception e) {
            log.error("方法{},发生异常!{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;
    }

    @ApiOperation(value = "查询机场信息V2", notes = "根据机场码等信息查询机场信息")
    @PostMapping(value = "airPortInfosV2",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResultDTO<List<AirPortInfoDTO>> getAllAirPortInfoV2(@RequestBody BaseRequestDTO<AirPortInfoReqDTO> requestDTO, HttpServletRequest request, BindingResult validResult) {
        BaseResultDTO<List<AirPortInfoDTO>> resultDTO = new BaseResultDTO<>();
        BeanUtils.copyNotNullProperties(requestDTO,resultDTO);
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultDTO.setErrorMsg(strMess);
            resultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return resultDTO;
        }
        try {
            resultDTO = flightBasicService.searchAirportV2(requestDTO);
        } catch (Exception e) {
            log.error("方法{},发生异常!{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;
    }
    @Deprecated
    @ApiOperation(value = "查询航线信息V1", notes = "根据出发、到达机场码等信息查询航线信息，目前与getAllAirLineV2一致")
    @PostMapping(value = "airLines",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResultDTO<List<AirLineDTO>> getAllAirLine(@RequestBody @Validated BaseRequestDTO<AirLineReqDTO> requestDTO, HttpServletRequest request, BindingResult validResult) {
        BaseResultDTO<List<AirLineDTO>> resultDTO = new BaseResultDTO<>();
        BeanUtils.copyNotNullProperties(requestDTO,resultDTO);
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultDTO.setErrorMsg(strMess);
            resultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return resultDTO;
        }
        try {
            resultDTO = flightBasicService.searchAirLine(requestDTO);
        } catch (Exception e) {
            log.error("方法{},发生异常!", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;
    }

    @ApiOperation(value = "查询航线信息V2", notes = "根据出发、到达机场码等信息查询航线信息，目前与getAllAirLine一致")
    @PostMapping(value = "airLinesV2",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResultDTO<List<AirLineDTO>> getAllAirLineV2(@RequestBody @Validated BaseRequestDTO<AirLineReqDTO> requestDTO, HttpServletRequest request, BindingResult validResult) {
        log.info("【接口：{}，请求参数：{}】", request.getRequestURI(), JsonUtil.objectToJson(requestDTO));
        BaseResultDTO<List<AirLineDTO>> resultDTO = new BaseResultDTO<>();
        BeanUtils.copyNotNullProperties(requestDTO,resultDTO);
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultDTO.setErrorMsg(strMess);
            resultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return resultDTO;
        }
        try {
            resultDTO = flightBasicService.searchAirLineV2(requestDTO);
        } catch (Exception e) {
            log.error("查询航线信息V2发生异常! 异常原因：", e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;
    }

    @ApiOperation(value = "批量添加航线", notes = "用于job添加航线")
    @PostMapping("airLines/addBatch")
    public BaseResultDTO addBatchAirLines(@RequestBody BaseRequestDTO<List<AirLineDTO>> requestDTO, HttpServletRequest request) {
        log.info("【接口：{}，请求参数：{}】", request.getRequestURI(), JsonUtil.objectToJson(requestDTO));
        BaseResultDTO resultDTO = new BaseResultDTO();

        try {
            resultDTO = flightBasicService.addBatchAirLines(requestDTO);

        } catch (Exception e) {
            log.error("方法{},发生异常!{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        log.info("【接口{},返回结果{}】", request.getRequestURI(), JsonUtil.objectToJson(resultDTO));
        return resultDTO;
    }

    @ApiOperation(value = "查询合作航司",notes = "根据公司代码、公司名、或者英文名查询")
    @PostMapping("cooperationAirlineList")
    public BaseResultDTO<List<com.juneyaoair.flightbasic.response.cooperationAirLine.CooperationAirlineDTO>> searchAllCooperationAirline(@RequestBody BaseRequestDTO<CooperationAirlineReqDTO> requestDTO, HttpServletRequest request, BindingResult validResult) {
        log.info("【接口：{}，请求参数：{}】", request.getRequestURI(), JsonUtil.objectToJson(requestDTO));
        BaseResultDTO<List<CooperationAirlineDTO>> result = new BaseResultDTO<>();
        BeanUtils.copyNotNullProperties(requestDTO,result);
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            result.setErrorMsg(strMess);
            result.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return result;
        }
        try {
            result = flightBasicService.listCooperationAirline(requestDTO);
        } catch (Exception e) {
            log.error("方法{},发生异常!{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            result.setResultCode(WSEnum.ERROR.resultCode);
            result.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        log.info("【接口{},返回结果{}】", request.getRequestURI(), JsonUtil.objectToJson(result));
        return result;
    }

    @ApiOperation(value = "查询航班信息",notes = "根据出发、到达机场等信息查询，出发到达机场为必须字段")
    @PostMapping("flightInfoList")
    public BaseResultDTO<List<FlightInfoDTO>> searchflightInfoList(@RequestBody @Validated BaseRequestDTO<FlightInfoReqDTO> requestDTO, HttpServletRequest request, BindingResult validResult) {
        BaseResultDTO<List<FlightInfoDTO>> resultDTO = new BaseResultDTO<>();
        BeanUtils.copyNotNullProperties(requestDTO,resultDTO);
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultDTO.setErrorMsg(strMess);
            resultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return resultDTO;
        }
        try {
            resultDTO = flightBasicService.searchflightInfoList(requestDTO);
        } catch (Exception e) {
            log.error("方法{},发生异常!{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;
    }

    @ApiOperation(value = "查询主题航班信息",notes = "根据出发、到达机场等信息查询，出发到达机场为必须字段")
    @PostMapping(value = "/searchThemeFlightInfoList")
    public BaseResultDTO<List<FlightInfoDTO>> searchThemeFlightInfoList(@RequestBody @Validated BaseRequestDTO<FlightInfoReqDTO> requestDTO, HttpServletRequest request, BindingResult validResult) {
        BaseResultDTO<List<FlightInfoDTO>> resultDTO = new BaseResultDTO<>();
        BeanUtils.copyNotNullProperties(requestDTO,resultDTO);
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultDTO.setErrorMsg(strMess);
            resultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return resultDTO;
        }
        try {
            resultDTO = flightBasicService.searchThemeFlightInfoList(requestDTO);
        } catch (Exception e) {
            log.error("方法{},发生异常!", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;
    }

    @ApiOperation(value = "根据航班日期查询主题航班计划",notes = "根据航班日期查询主题航班计划")
    @PostMapping(value = "/searchThemeFlightByDate")
    public BaseResultDTO<List<ThemeAirline>> searchFlightByDate(@RequestBody @Validated BaseRequestDTO<ThemeFlightParam> requestDTO, HttpServletRequest request, BindingResult validResult) {
        if (validResult.hasErrors()) {
            throw new IllegalArgumentException(validResult.getAllErrors().get(0).getDefaultMessage());
        }
        ObjCheckUtil.isNull(requestDTO.getRequest(),"业务参数不可为空");
        return BaseResultDTO.newSuccess(flightBasicService.searchFlightByDate(requestDTO.getRequest()));
    }

    @ApiOperation(value = "主题航班日历",notes = "主题航班日历")
    @PostMapping(value = "/selectThemeCalendar")
    public BaseResultDTO<List<String>> selectThemeCalendar(@RequestBody @Validated BaseRequestDTO requestDTO, HttpServletRequest request, BindingResult validResult) {
        if (validResult.hasErrors()) {
            throw new IllegalArgumentException(validResult.getAllErrors().get(0).getDefaultMessage());
        }
        return BaseResultDTO.newSuccess(flightBasicService.searchThemeCalendar());
    }



    @ApiOperation(value = "查询共飞城市",notes = "对应航班查询邻近航线、热门推荐模块")
    @PostMapping("queryCommonFlyCityDetailsByCityCode")
    public Map queryCommonFlyCityDetailsByCityCode(@RequestBody @Validated CommonFlyCityReqDTO requestDTO, BindingResult validResult,HttpServletRequest request) {
        log.info("【接口：{}，请求参数：{}】", request.getRequestURI(), JsonUtil.objectToJson(requestDTO));
        Map<String, Object> resultMap = new HashMap<>();
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultMap.put("resultCode", WSEnum.ERROR.resultCode);
            resultMap.put("resultMsg", strMess);
            return resultMap;
        }
        try {
            resultMap = flightBasicService.queryCommonFlyCityDetailsByCityCode(requestDTO);
        }catch (Exception e){
            log.error("方法{},发生异常!{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultMap.put("resultCode", WSEnum.ERROR.resultCode);
            resultMap.put("resultMsg", WSEnum.ERROR.resultInfo);
        }
        return resultMap;
    }

    @PostMapping("searchTrrFlightInfo")
    public BaseResultDTO<List<TrrFlightInfoDTO>> searchTrrFlightinfo(@RequestBody @Validated  BaseRequestDTO<ParamTrrFlightInfo> requestDTO, BindingResult validResult, HttpServletRequest request) {
        BaseResultDTO<List<TrrFlightInfoDTO>> resultDTO = new BaseResultDTO<>();
        BeanUtils.copyNotNullProperties(requestDTO, resultDTO);
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultDTO.setErrorMsg(strMess);
            resultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return resultDTO;
        }
        try {
            resultDTO = flightBasicService.searchTrrFlightinfo(requestDTO);
        } catch (Exception e) {
            log.error("方法{},发生异常!", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;

    }

    @ApiOperation(value = "查询价格Y舱价格",notes = "根据出发城市,到达城市,时间范围来查询Y舱价格")
    @PostMapping("searchFlightTicketPriceByYAndCity")
    public BaseResultDTO<List<FlightTicketPriceByYResp>> searchFlightTicketPriceByYAndCity(@Validated @RequestBody BaseRequestDTO<List<FlightTicketPriceByYDTO>> requestDTO, BindingResult validResult){
        BaseResultDTO<List<FlightTicketPriceByYResp>> resultDTO = new BaseResultDTO<>();
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            resultDTO.setErrorMsg(strMess);
            resultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return resultDTO;
        }

        try {
            resultDTO = flightBasicService.searchFlightTicketPriceByYAndCity(requestDTO);
        } catch (Exception e) {
            log.error("类{},方法{},抛出异常!{}", this.getClass().getName(), Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;
    }

}
