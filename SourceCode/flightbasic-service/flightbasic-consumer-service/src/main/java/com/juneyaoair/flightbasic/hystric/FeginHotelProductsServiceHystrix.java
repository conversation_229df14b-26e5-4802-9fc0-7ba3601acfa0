package com.juneyaoair.flightbasic.hystric;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.feign.basic.FeginHotelProductsService;
import com.juneyaoair.flightbasic.request.city.HotelProductReqDTO;
import com.juneyaoair.flightbasic.response.city.HotelProductRespDTO;
import com.juneyaoair.flightbasic.response.country.TCountryDTO;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2020-12-14 9:53
 * @description：
 * @modified By：
 * @version: $
 */
@Component
public class FeginHotelProductsServiceHystrix implements FeginHotelProductsService {
    @Override
    public BaseResultDTO<List<HotelProductRespDTO>> queryHotelProducts(BaseRequestDTO<HotelProductReqDTO> requestDTO) {
        BaseResultDTO<List<HotelProductRespDTO>> baseResultDTO = new BaseResultDTO();
        baseResultDTO.setResultCode(WSEnum.SERVICE_FUSING.resultCode);
        baseResultDTO.setErrorMsg(WSEnum.SERVICE_FUSING.resultInfo);
        return baseResultDTO;
    }
}
