package com.juneyaoair.flightbasic.service.api.impl;

import com.juneyaoair.flightbasic.appenum.ChannelCodeEnum;
import com.juneyaoair.flightbasic.mapper.api.AppVersionManageMapper;
import com.juneyaoair.flightbasic.mapper.api.DICTValueMapper;
import com.juneyaoair.flightbasic.model.api.AppVersionManagePO;
import com.juneyaoair.flightbasic.redis.MobileAPIRedisUtil;
import com.juneyaoair.flightbasic.service.api.CacheService;
import com.juneyaoair.utils.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class CacheServiceImpl implements CacheService {

    @Resource
    private MobileAPIRedisUtil mobileAPIRedisUtil;

    @Autowired
    private AppVersionManageMapper appVersionManageMapper;

    @Autowired
    private DICTValueMapper dictValueMapper;

    private static final String MOBILE_MODULE_TAB_CACHE = "common:middleTabService";

    private static final String DTCODE_IN_TP_DICTTYPE = "CHANNEL";

    @Override
    public void clearModuleManagementCache() {
        try {
            // 生成所有可能的键组合
            Set<String> allPossibleKeys = generateAllPossibleKeys();
            log.info("可能的键的组合数为：{}，组合为：{}",allPossibleKeys.size(),allPossibleKeys);

            // 批量删除存在的键
            if (!allPossibleKeys.isEmpty()) {
                long deletedCount = mobileAPIRedisUtil.deleteKeys(allPossibleKeys);
                log.info("成功删除 {} 个缓存键", deletedCount);
            } else {
                log.info("未找到需要清理的缓存键");
            }
        } catch (Exception e) {
            log.error("模块管理缓存清除失败! 异常信息:{}：", e.getMessage());
        }
    }

    private Set<String> generateAllPossibleKeys() {
        Set<String> keys = new HashSet<>();
        List<String> channels = dictValueMapper.getDVCodeByDTCode(DTCODE_IN_TP_DICTTYPE);
        List<AppVersionManagePO> systemsAndVersions = appVersionManageMapper.getNotForceUpdateDeviceTypesAndActiveVersions();

        // 生成所有组合
        for (String channel : channels) {
            if(ChannelCodeEnum.MOBILE.name().equals(channel)||ChannelCodeEnum.G_MOBILE.name().equals(channel)){
                for (AppVersionManagePO systemsAndVersion : systemsAndVersions) {
                    // 渠道名+系统名+版本号
                    keys.add(MOBILE_MODULE_TAB_CACHE + ":" + channel + ":" + systemsAndVersion.getDeviceType() + ":" + systemsAndVersion.getAppVersionNo());
                }
            }else{
                // 渠道名
                keys.add(MOBILE_MODULE_TAB_CACHE + ":" + channel);
            }
        }
        return keys;
    }

    /**
     * @param channelCode
     * @param depCityCode
     * @param arrCityCode
     * @param days
     * @return void
     * @description 清理指定的价格缓存key
     * 参照key
     * API:common:flightMinPrice:MOBILE:MCNYOW2025-05-08SHAOSA
     * API:common:flightTaxMinPrice:MOBILE:MCNYOW2025-05-08SHAOSA
     * <AUTHOR>
     * @date 2025/5/8 11:02
     **/
    @Override
    public void clearMinPrice(String channelCode, String depCityCode, String arrCityCode, String startDateStr, Integer days) {
        if (days < 0) {
            return;
        }
        String airline = depCityCode + arrCityCode;
        String airlineBack = arrCityCode + depCityCode;
        if (StringUtils.isBlank(startDateStr)) {
            startDateStr = DateUtils.getCurrentDateStr();
        }
        Date startDate = DateUtils.convertStr2Date(startDateStr, DateUtils.STRING_FORMAT_YYYY_MM_DD);
        String common = "common:flightMinPrice:" + channelCode + ":MCNYOW";
        String commonTax = "common:flightTaxMinPrice:" + channelCode + ":MCNYOW";
        for (int i = 0; i <= days; i++) {
            Date date = DateUtils.addOrLessDay(startDate, i);
            String dateStr = DateUtils.convertDate2Str(date, DateUtils.STRING_FORMAT_YYYY_MM_DD);
            common = common + dateStr;
            commonTax = commonTax + dateStr;
            String key = common + airline;
            //String keyBack = common + airlineBack;
            String keyTax = commonTax + airline;
            //String keyTaxBack = commonTax + airlineBack;
            mobileAPIRedisUtil.removeBatch(key, keyTax);
        }
    }
}
