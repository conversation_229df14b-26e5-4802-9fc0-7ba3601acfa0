package com.juneyaoair.flightbasic.feign.basic;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.hystric.CooperationAirLineHystrix;
import com.juneyaoair.flightbasic.request.cooperationAirLine.CooperationAirlineReqDTO;
import com.juneyaoair.flightbasic.response.cooperationAirLine.CooperationAirlineDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "flightbasic-provider-service",fallback = CooperationAirLineHystrix.class, contextId = "FeignCooperationAirLineService")
public interface FeignCooperationAirLineService {
    @PostMapping("/basic/cooperationAirlineList")
     BaseResultDTO<List<CooperationAirlineDTO>> searchAllCooperationAirline(@RequestBody BaseRequestDTO<CooperationAirlineReqDTO> requestDTO);
}
