package com.juneyaoair.flightbasic.controller;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.external.service.ShaCustomsService;
import com.juneyaoair.flightbasic.response.flightInfo.FlightInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api(description = "上海海关服务")
@RestController
public class ShaCustomsController {

    @Autowired
    private ShaCustomsService shaCustomsService;

    @ApiOperation(value = "推送数据给上海海关")
    @PostMapping("/shaCustoms/sendToCustoms")
    public BaseResultDTO<Object> sendToCustoms(@RequestBody BaseRequestDTO<FlightInfoDTO> baseRequest) {
        FlightInfoDTO request = baseRequest.getRequest();
        shaCustomsService.sendToCustoms(request.getFlightNo(), request.getFlightDate(), request.getDepAirport(), request.getArrAirport());
        return BaseResultDTO.newSuccess(null);
    }
}
