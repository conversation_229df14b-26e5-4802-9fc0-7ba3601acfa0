package com.juneyaoair.flightbasic.controller.cms;

import com.juneyaoair.flightbasic.common.CmsPicDTO;
import com.juneyaoair.flightbasic.common.CustomerConstants;
import com.juneyaoair.flightbasic.request.cms.ContentDto;
import com.juneyaoair.flightbasic.request.cms.ContentQueryDto;
import com.juneyaoair.flightbasic.service.cms.ContentService;
import com.juneyaoair.utils.json.JsonUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 16:43 2019/3/1
 * @Modified by:
 */
@RestController
@RequestMapping("/content")
@Slf4j
@Api(description = "CMS基础服务api")
public class ContentBasicController {

    @Value("${cms.http.path}")
    private  String httpPath;
    @Value("${cms.pic.url}")
    private String picUrl;
    @Autowired
    private ContentService contentService;

    @PostMapping("/JSonAppPicCarousel")
    public CmsPicDTO<List<ContentDto>> queryContent(@RequestBody ContentQueryDto requestDTO, HttpServletRequest request){
        log.info("【接口：{}，请求参数：{}】", request.getRequestURI(), JsonUtil.objectToJson(requestDTO));
        CmsPicDTO<List<ContentDto>> list =  contentService.searchList(requestDTO);
        List<ContentDto> list1 = list.getAppPicCarousel();
        List<ContentDto> temp = new ArrayList<>();
        for(ContentDto dto:list1){
            dto.setEnd_time(dto.getEnd_time().substring(0,19));
            dto.setStart_time(dto.getStart_time().substring(0,19));
            if(!StringUtils.isEmpty(dto.getHttpPath())){
                dto.setHttpPath(httpPath + dto.getHttpPath());
            }
            if(!StringUtils.isEmpty(dto.getPic_url())){
                dto.setPic_url(picUrl + dto.getPic_url());
            }

            temp.add(dto);
        }
        list.setAppPicCarousel(temp);
        System.out.println(JsonUtil.objectToJson(list));
        return list;

    }
}
