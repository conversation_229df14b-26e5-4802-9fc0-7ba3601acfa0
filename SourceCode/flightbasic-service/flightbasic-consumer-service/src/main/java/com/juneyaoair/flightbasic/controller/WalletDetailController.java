package com.juneyaoair.flightbasic.controller;

import com.alibaba.fastjson.JSON;
import com.juneyaoair.flightbasic.WalletDetailReq;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.feign.basic.FeignWalletDetailService;
import com.juneyaoair.flightbasic.response.WalletDetailResp;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName WalletDetailController
 * @Description 钱包详情相关接口
 * @createTime 2023年04月04日 14:16
 */
@RestController
@RequestMapping(value = "wallet")
@Api(description = "钱包详情相关接口")
@Slf4j
public class WalletDetailController {

    @Autowired
    private FeignWalletDetailService feignWalletDetailService;

    @PostMapping(value = "/queryWalletDetail")
    public WalletDetailResp queryWalletDetail (@RequestBody @Validated WalletDetailReq walletDetailReq, BindingResult validResult){
        log.info(String.format("[WalletDetailController.%s][请求信息: %s ]", "queryWalletDetail", JSON.toJSONString(walletDetailReq)));
        WalletDetailResp result = new WalletDetailResp();
        try{
            if (validResult.hasErrors()) {
                String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
                result.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
                result.setErrorMsg(strMess);
                return result;
            }
            result = feignWalletDetailService.queryWalletDetail(walletDetailReq);
        }catch (Exception e){
            String msg = String.format("[WalletDetailController.%s] %s [异常信息: %s ] [堆栈信息: %s ] [请求信息: %s ]", "queryWalletDetail", "查询钱包详情异常！"
                    , e.getMessage(), JSON.toJSONString(e.getStackTrace()), JSON.toJSONString(result));
            log.error(msg);
        }
        log.info(String.format("[WalletDetailController.%s][返回信息: %s ]", "queryWalletDetail", JSON.toJSONString(result)));
        return result;
    }

}
