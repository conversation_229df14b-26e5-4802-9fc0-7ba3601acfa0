package com.juneyaoair.flightbasic.fliter;


import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.utils.IpUtils;
import com.juneyaoair.flightbasic.utils.MdcUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.UUID;

@Component
public class CustomerFilter implements Filter {

    @ApolloJsonValue("${juneyaoair.noLogPath.patterns:[\"/actuator/info\",\"/actuator/health\",\"/actuator\"]}")
    private List<String> excludePathPatterns;

    private static final Logger log = LoggerFactory.getLogger(CustomerFilter.class);

    //Filter初始化
    @Override
    public void init(FilterConfig arg0) throws ServletException {

    }

    //Filter销毁
    @Override
    public void destroy() {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        // 对request、response进行一些预处理
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        HttpServletRequest servletRequest = (HttpServletRequest) request;
        if (servletRequest.getServletPath().toString().indexOf("swagger") > -1 || ((HttpServletRequest) request).getServletPath().toString().indexOf("api-docs") > -1) {
            chain.doFilter(request, response);
            return;
        }
        MdcUtils.setRequestId(UUID.randomUUID().toString());
        long startTime = System.currentTimeMillis();
        //获取方法
        String method = servletRequest.getMethod();
        //获取IP地址
        String clientIP = IpUtils.getIpAddr(servletRequest);;
        BaseResultDTO resultInfo = new BaseResultDTO();
        //获取绝对路径
        String urlPath = servletRequest.getRequestURI();
        //请求体
        String requestBody = getRequestBody(servletRequest);
        WrapperedRequest wrapRequest = new WrapperedRequest(servletRequest, requestBody);
        //记录日志
        boolean logFlag = isPrintLog(urlPath);
        if (logFlag) {
            log.info("请求ID：{}请求地址：{}【客户端IP】:{} 【请求信息】:{}", MdcUtils.getRequestId(), urlPath, clientIP, requestBody);
        }

        WrapperedResponse wrapResponse = new WrapperedResponse((HttpServletResponse) response);
        //设置允许跨域的配置
        //这里填写你允许进行跨域的主机ip（正式上线时可以动态配置具体允许的域名和IP）
        wrapResponse.setHeader("Access-Control-Allow-Origin", "*");
        // 允许的访问方法
        wrapResponse.setHeader("Access-Control-Allow-Methods", "POST, GET, PUT, OPTIONS, DELETE, PATCH");
        // Access-Control-Max-Age 用于 CORS 相关配置的缓存
        wrapResponse.setHeader("Access-Control-Max-Age", "3600");
        wrapResponse.setHeader("Access-Control-Allow-Headers", "token,Origin, X-Requested-With, Content-Type, Accept");
        if ("OPTIONS".equals(method)) {
            wrapResponse.setStatus(HttpServletResponse.SC_OK);
        } else {
            String ResponseBody = "";
            try {
                // 执行目标资源，放行
                chain.doFilter(wrapRequest, wrapResponse);
                //请求返回结果日志记录
                byte[] data = wrapResponse.getResponseData();
                ResponseBody = new String(data, "utf-8");
            } catch (Exception ex) {
                resultInfo.fail(WSEnum.SYS_EXCEPTION.resultCode, String.format("错误信息：%s", getExceptionAllinformation(ex)));
                ResponseBody = JSON.toJSONString(resultInfo);
            }
            long endTime = System.currentTimeMillis();
            long interval = endTime - startTime;
            writeResponse(response, ResponseBody);
            if (logFlag) {
                log.info("请求ID：{} 【客户端IP】:{} 【返回信息】:{} 【响应时间】:{}", MdcUtils.getRequestId(), clientIP, ResponseBody, interval);
            }
        }
        MdcUtils.clear();
    }

    /**
     * true 表示打印日志 false 表示不打印日志
     * @return
     */
    private boolean isPrintLog(String path){
        AntPathMatcher antPathMatcher = new AntPathMatcher();
        if(CollectionUtils.isEmpty(excludePathPatterns)){
            return true;
        }
        // 表示不进行日志输出
        if (excludePathPatterns.stream().anyMatch(url -> antPathMatcher.match(url, path))) {
            return false;
        }
        return true;
    }

    //获取错误的方法
    private String getExceptionAllinformation(Exception ex) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        PrintStream pout = new PrintStream(out);
        ex.printStackTrace(pout);
        String ret = new String(out.toByteArray());
        pout.close();
        try {
            out.close();
        } catch (Exception e) {
        }
        return ret;
    }

    /**
     * @param req
     * @return
     */
    private String getRequestBody(HttpServletRequest req) {
        try {
            BufferedReader reader = req.getReader();
            StringBuffer sb = new StringBuffer();
            String line = null;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            String json = sb.toString();
            return json;
        } catch (IOException e) {

        }
        return "";
    }

    //输出信息
    private void writeResponse(ServletResponse response, String responseString) throws IOException {
        PrintWriter out = response.getWriter();
        out.print(responseString);
        out.flush();
        out.close();
    }
}
