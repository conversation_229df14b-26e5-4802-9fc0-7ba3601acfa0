package com.juneyaoair.flightbasic.controller;



import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.service.discountsroutecache.IDiscountsRouteCacheService;
import com.juneyaoair.flightbasic.utils.MdcUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RequestMapping("discountsRouteCache")
@Slf4j
@RestController
@Api(description = "获取特惠航线数据")
public class DiscountsRouteCacheController {

    @Autowired
    private IDiscountsRouteCacheService discountsRouteCacheService;


    @ApiOperation(value = "航线价格缓存刷新", notes = "航线价格缓存刷新")
    @PostMapping("refreshPriceCache")
    public BaseResultDTO refreshPriceCache(HttpServletRequest request){
        log.info("【接口：{}，请求ip：{}】", request.getRequestURI(), MdcUtils.getRequestId());
        BaseResultDTO baseResultDTO = new BaseResultDTO();
        discountsRouteCacheService.cacheDiscountsRoutePrice();
        baseResultDTO.success();
        return baseResultDTO;
    }
}
