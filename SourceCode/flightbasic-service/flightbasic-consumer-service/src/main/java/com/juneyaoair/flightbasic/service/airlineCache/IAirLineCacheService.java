package com.juneyaoair.flightbasic.service.airlineCache;

import com.juneyaoair.flightbasic.airline.AirLineCache;
import com.juneyaoair.flightbasic.appenum.ChannelCodeEnum;
import com.juneyaoair.flightbasic.bean.AirLinePriceRule;
import org.springframework.scheduling.annotation.Async;

/**
 * Created by yaocf on 2023/1/9  8:50.
 */
public interface IAirLineCacheService {

    /**
     * 根据航班日期，刷新指定的城市对低价信息
     * @param flightDate
     * @param depCityCode
     * @param arrCityCode
     * @param interFlag
     * @param channelCode
     */
    void refreshAirlinePriceCache(String flightDate, String depCityCode, String arrCityCode, String interFlag, ChannelCodeEnum channelCode);

    /**
     * 接口调用刷新缓存
     * @param airLineCache
     */
    @Async("taskExecutor")
    void refreshAirlinePriceCache(AirLineCache airLineCache);
    /**
     * 查询每日航班数据，根据出发到达去重处理
     * @param airLinePriceRule
     * @return
     */
    void queryFlightAirline(AirLinePriceRule airLinePriceRule);

    /**
     *
     * @param jobParam  任务参数
     * @param curIndex  当前片
     * @param shardTotal 总片数
     */
    @Async("taskExecutor")
    void queryFlightAirline(String jobParam,int curIndex,int shardTotal);


    /**
     * 刷新低价日历缓存
     * @param jobParam
     * @param curIndex
     * @param shardTotal
     */
    @Async("taskExecutor")
    void queryLowPriceCalendarAirline(String jobParam, int curIndex, int shardTotal);

    void queryLowPriceCalendarAirline(AirLinePriceRule airLinePriceRule);
}
