package com.juneyaoair.flightbasic.hystric;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.feign.memberCenter.FeignMemberCenterService;
import com.juneyaoair.flightbasic.redis.FlightBasicRedisUtil;
import com.juneyaoair.flightbasic.redis.RedisKeyConstants;
import com.juneyaoair.flightbasic.request.memberCenter.ParamMemberRightsDTO;
import com.juneyaoair.flightbasic.response.memberCenter.MemberLevelDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class MemberCenterHystrix implements FeignMemberCenterService {
    private Gson gson = new Gson();
    @Autowired
    private FlightBasicRedisUtil redisUtil;

    @Override
    public BaseResultDTO<List<MemberLevelDTO>> searchMemberRight(BaseRequestDTO<ParamMemberRightsDTO> requestDTO) {
        BaseResultDTO<List<MemberLevelDTO>> resultDTO = new BaseResultDTO<>();
        try {
            String redisResult = redisUtil.get(RedisKeyConstants.MEMBER_RIGHTS_MESSAGE);
            List<MemberLevelDTO> listMemberRightsDTO = gson.fromJson(redisResult, new TypeToken<List<MemberLevelDTO>>() {
            }.getType());
            resultDTO.setResult(listMemberRightsDTO);
            resultDTO.setErrorMsg(WSEnum.SUCCESS.resultInfo);
            resultDTO.setResultCode(WSEnum.SUCCESS.resultCode);
        } catch (JsonSyntaxException e) {
            log.error("会员权益信息查询接口熔断，并从redis获取信息错误！",e);
            resultDTO.setErrorMsg(WSEnum.SERVICE_FUSING.resultInfo);
            resultDTO.setResultCode(WSEnum.SERVICE_FUSING.resultCode);
        }
        return resultDTO;
    }

}
