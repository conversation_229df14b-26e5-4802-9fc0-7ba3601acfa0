package com.juneyaoair.flightbasic.service.weChat.impl;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.feign.weChat.FeignWeChatPush;
import com.juneyaoair.flightbasic.request.wechat.PushMessageRequestDTO;
import com.juneyaoair.flightbasic.response.weChat.PushMessageResponseDTO;
import com.juneyaoair.flightbasic.service.weChat.WeChatPushService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: flightbasic
 * @description
 * @author: ZhangWanLi
 * @create: 2020-01-17 15:16
 **/
@Service
public class WeChatPushServiceImpl implements WeChatPushService {
    @Autowired
    private FeignWeChatPush feignWeChatPush;


    @Override
    public  BaseResultDTO<List<PushMessageResponseDTO>> searchPushMessage(BaseRequestDTO<PushMessageRequestDTO> pushMessageRequestDTO) {
        BaseResultDTO<List<PushMessageResponseDTO>> listBaseResultDTO = feignWeChatPush.searchPushMessage(pushMessageRequestDTO);

        return listBaseResultDTO;
    }

    @Override
    public BaseResultDTO modifyPushMessage(BaseRequestDTO<PushMessageRequestDTO> pushMessageRequestDTO) {
        BaseResultDTO BaseResultDTO = feignWeChatPush.modifyPushMessage(pushMessageRequestDTO);
        return BaseResultDTO;
    }

    @Override
    public BaseResultDTO reducePushCount(BaseRequestDTO<PushMessageRequestDTO> pushMessageRequestDTO) {
        BaseResultDTO BaseResultDTO = feignWeChatPush.reducePushCount(pushMessageRequestDTO);
        return BaseResultDTO;
    }
}
