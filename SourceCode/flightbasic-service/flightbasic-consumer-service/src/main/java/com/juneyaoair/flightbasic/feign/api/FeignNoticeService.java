package com.juneyaoair.flightbasic.feign.api;

import com.juneyaoair.flightbasic.common.BaseReq;
import com.juneyaoair.flightbasic.common.BaseResponseDTO;
import com.juneyaoair.flightbasic.hystric.NoticeHystrix;
import com.juneyaoair.flightbasic.request.notice.NoticeRequestDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;


/**
 * @program: flightbasic
 * @description
 * @author: zhang<PERSON>an<PERSON><PERSON>
 * @create: 2019-08-28 15:17
 **/
@Service
@FeignClient(value = "flightbasic-provider-service" ,fallback = NoticeHystrix.class, contextId = "FeignNoticeService")
public interface FeignNoticeService {
    @RequestMapping(value = "indexHandle/getAllNoticeInfo")
    BaseResponseDTO  getAllNoticeInfo(@RequestBody BaseReq<NoticeRequestDTO> requestDTO);

    @RequestMapping(value = "indexHandle/getRichTextNoticeInfo")
    BaseResponseDTO  getRichTextNoticeInfo(@RequestBody BaseReq<NoticeRequestDTO> requestDTO);

}
