package com.juneyaoair.flightbasic.feign.basic;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.hystric.FeginHotelProductsServiceHystrix;
import com.juneyaoair.flightbasic.hystric.SchedualServiceHystric;
import com.juneyaoair.flightbasic.request.city.HotelProductReqDTO;
import com.juneyaoair.flightbasic.response.city.HotelProductRespDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * @Author:guanshiyin
 * @Description:
 * @Date:Created in 13:33 2020/12/11
 * @Modified by:
 */
@Service
@FeignClient(value = "flightbasic-provider-service",fallback = FeginHotelProductsServiceHystrix.class, contextId = "FeginHotelProductsService")
public interface FeginHotelProductsService {
    @RequestMapping("/hotelProducts/queryHotelProduct")
    BaseResultDTO<List<HotelProductRespDTO>> queryHotelProducts(@RequestBody BaseRequestDTO<HotelProductReqDTO> requestDTO);

}
