package com.juneyaoair.flightbasic.controller;

import com.juneyaoair.flightbasic.common.BaseReq;
import com.juneyaoair.flightbasic.common.BaseResponseDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.request.dict.DICTValueDTO;
import com.juneyaoair.flightbasic.request.notice.NoticeRequestDTO;
import com.juneyaoair.flightbasic.request.notice.request.Message;
import com.juneyaoair.flightbasic.request.notice.resposne.NoticeResponse;
import com.juneyaoair.flightbasic.request.resource.ResourceManaDTO;
import com.juneyaoair.flightbasic.request.versionManage.VersionInfoDTO;
import com.juneyaoair.flightbasic.request.versionManage.VersionManageDTO;
import com.juneyaoair.flightbasic.request.versionManage.VersionManageTwoDTO;
import com.juneyaoair.flightbasic.request.wechat.*;
import com.juneyaoair.flightbasic.response.flightInfo.AliBindingDTO;
import com.juneyaoair.flightbasic.response.icon.AppPicturePartDTO;
import com.juneyaoair.flightbasic.response.versionManage.AppVersionManageDTO;
import com.juneyaoair.flightbasic.service.DataHandleService.*;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.StringUtil;
import com.juneyaoair.utils.util.VersionNoUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;


/**
 * Created by yaocf on 2018/4/26  14:45.
 */
@RequestMapping("indexHandle")
@RestController
@Slf4j
@Api(description = "app调用基础服务api")
public class IndexController {


    @Autowired
    private IAppVersionManageService appVersionManageService;
    @Autowired
    private IMessageService messageService;
    @Autowired
    private ISpecialAirLineService specialAirLineService;
    @Autowired
    private IPictureService pictureService;
    @Autowired
    private IVersionMangeService versionMangeService;
    @Autowired
    private AppPictureService appPictureService;
    @Autowired
    private IResourceManaService resourceManaService;
    @Resource
    private IWxModularVersionManageService modularVersionManageService;
    @Autowired
    private DICTValueService dictValueService;
    @Autowired
    private INoticeService noticeService;
    @Autowired
    private IAliBindingService aliBindingService;

    @Resource
    private NavigationService navigationService;

    private static final String HOLIDAY_REDIS_KEY = "holidayCalender";


    //查询公告
    @PostMapping(value = "getMessageList")
    @ApiOperation(value = "查询app通知公告消息", notes = "根据 消息标题 ,消息类型等查询公告详情")
    public Object getMessage(@RequestBody MessageRequestDTO messageRequest, HttpServletRequest request) {
        BaseResponseDTO resp = new BaseResponseDTO();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<MessageRequestDTO>> violations = validator.validate(messageRequest);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        try {
            MessageDTO message = new MessageDTO();
            message.setChannels(messageRequest.getChannelCode());
            message.setMessageEndTime(DateUtils.getCurrentDateStr());
            message.setMessageIsTop(messageRequest.getIsTop());
            message.setMessageType(messageRequest.getMessageTypeCode());
            message.setMessageStatus("Y");
            message.setOrderBy("MESSAGE_STARTTIME DESC,MESSAGE_ENDTIME DESC,MESSAGE_UPDATETIME DESC");
            List<MessageDTO> messageList = messageService.getActiveMsgList(message);
            List<MessDTO> messList = new ArrayList<>();
            if (!StringUtil.isNullOrEmpty(messageList)) {
                for (MessageDTO m : messageList) {
                    MessDTO mess = new MessDTO();
                    BeanUtils.copyNotNullProperties(m, mess);
                    messList.add(mess);
                }
            }
            resp.setObjData(messList);
            resp.setChannelCode(messageRequest.getChannelCode());
            resp.setResultCode(WSEnum.SUCCESS.resultCode);
            return resp;

        } catch (Exception e) {
            log.error("方法{},发生异常!{},请求参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e.getMessage(), messageRequest);
            resp.setObjData(null);
            resp.setResultCode(WSEnum.ERROR.resultCode);
            resp.setResultInfo(WSEnum.ERROR.resultInfo);
            return resp;
        }
    }


    //获取APP公告列表2.0
    @PostMapping(value = "fetchMessageList")
    @ApiOperation(value = "获取APP公告列表2.0", notes = "获取APP公告列表2.0")
    public BaseResponseDTO<List<NoticeResponse>> fetchMessageList(@RequestBody @Validated MessageRequestDTO messageRequest, BindingResult bindingResult, HttpServletRequest request) {
        BaseResponseDTO<List<NoticeResponse>> resp = new BaseResponseDTO<>();
        //校验参数
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        try {
            Message message = new Message();
            message.setChannels(messageRequest.getChannelCode());
            message.setMessageEndTime(DateUtils.getCurrentDateStr());
            message.setMessageIsTop(messageRequest.getIsTop());
            message.setMessageType(messageRequest.getMessageTypeCode());
            message.setMessageStatus("Y");
            message.setOrderBy("MESSAGE_STARTTIME DESC,MESSAGE_ENDTIME DESC,MESSAGE_UPDATETIME DESC");
            List<NoticeResponse> noticeResponses = messageService.fetchMessageList(message);
            resp.setObjData(noticeResponses);
            resp.setChannelCode(messageRequest.getChannelCode());
            resp.setResultCode(WSEnum.SUCCESS.resultCode);
            return resp;

        } catch (Exception e) {
            log.error("方法{},发生异常!{},请求参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e.getMessage(), messageRequest);
            resp.setObjData(null);
            resp.setResultCode(WSEnum.ERROR.resultCode);
            resp.setResultInfo(WSEnum.ERROR.resultInfo);
            return resp;
        }
    }

    @PostMapping(value = "clearMessCache")
    @ApiOperation(value = "公告缓存清理", notes = "公告缓存清理")
    public BaseResponseDTO clearMessCache(){
        messageService.clearMessRedis();
        return BaseResponseDTO.result(WSEnum.SUCCESS);
    }


    /**
     * 根据公告ID查询公告详情
     *
     * @param messageRequest
     * @param request
     * @return
     */
    @PostMapping(value = "getMessgeDetail")
    @ApiOperation(value = "根据公告ID查询公告详情", notes = "根据公告ID查询公告详情")
    public Object getMessageDetail(@RequestBody MessageRequestDTO messageRequest, HttpServletRequest request) {
        BaseResponseDTO<Object> resp = new BaseResponseDTO<>();
        try {
            String messageId = messageRequest.getMessageId();
            if (StringUtils.isEmpty(messageId)) {
                resp.setObjData(null);
                resp.setChannelCode(messageRequest.getChannelCode());
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
                resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS.resultInfo);
                return resp;
            }
            MessageDTO message = new MessageDTO();
            message.setMessageId(messageRequest.getMessageId());
            MessageDTO activeMsgDetail = messageService.getActiveMsgDetail(message);
            MessDetail messDetail = new MessDetail();
            if (null != activeMsgDetail) {
                BeanUtils.copyProperties(activeMsgDetail, messDetail);
            }
            resp.setObjData(messDetail);
            resp.setChannelCode(messageRequest.getChannelCode());
            resp.setResultCode(WSEnum.SUCCESS.resultCode);
            return resp;

        } catch (Exception e) {
            log.error("方法{},发生异常!{},请求参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e.getMessage(), messageRequest);
            resp.setObjData(null);
            resp.setResultCode(WSEnum.ERROR.resultCode);
            resp.setResultInfo(WSEnum.ERROR.resultInfo);
            return resp;
        }
    }


    //首页图片
    @PostMapping(value = "getPictureList")
    @ApiOperation(value = "app首页图片查询", notes = "依据渠道号 图片地址 平台信息 版本号等查询图片 ")
    public Object getPictures(@RequestBody PictureRequestDTO pictureRequest, HttpServletRequest request) {
        BaseResponseDTO resp = new BaseResponseDTO();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<PictureRequestDTO>> violations = validator.validate(pictureRequest);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        PictureDTO pic = new PictureDTO();
        pic.setChannelCode(pictureRequest.getChannelCode());//渠道信息
        pic.setPicLocation(pictureRequest.getPicLocation());
        pic.setEndTime(DateUtils.getCurrentDateTimeStr());
        pic.setType(pictureRequest.getType());
        pic.setLanguage(pictureRequest.getLanguage());
        try {
            String platFormInfo = pictureRequest.getPlatformInfo();//平台信息
            int verInt = VersionNoUtil.toVerInt(pictureRequest.getClientVersion());

            pic.setPlatformInfo(platFormInfo);//手机操作平台信息
            pic.setEnabledMode(pictureRequest.getEnableMode());
            pic.setVerInt(verInt);  //当前的客户端版本
            BaseResponseDTO<List<PictureDTO>> baseResponseDTO = pictureService.getValidList(pic);
            if (WSEnum.SUCCESS.resultCode.equals(baseResponseDTO.getResultCode())) {
                List<PictureDTO> pictureList = baseResponseDTO.getObjData();
                List<PicDTO> picList = new ArrayList<>();  //返回的图片集合
                if (!StringUtil.isNullOrEmpty(pictureList)) {
                    for (PictureDTO picture : pictureList) {
                        PicDTO picN = new PicDTO();
                        BeanUtils.copyNotNullProperties(picture, picN);
                        picN.setUrl(reWriteUrl(picture.getTitle(), picture.getTitleStyle(), picture.getUrl()));
                        picList.add(picN);
                    }
                    //查询模块名称
                    DICTValueDTO queryParam = new DICTValueDTO();
                    queryParam.setDvCode(pictureRequest.getPicLocation());
                    DICTValueDTO result = dictValueService.getDICTValue(queryParam);
                    if (result != null) {
                        resp.setRemark(result.getDvName());
                    }
                }
                resp.setResultCode(WSEnum.SUCCESS.resultCode);
                resp.setResultInfo(WSEnum.SUCCESS.resultInfo);
                resp.setObjData(picList);
                resp.setChannelCode(pictureRequest.getChannelCode());
            } else {
                resp.setResultCode(baseResponseDTO.getResultCode());
                resp.setResultInfo(baseResponseDTO.getResultInfo());
            }
            return resp;
        } catch (Exception e) {
            log.error("方法{},发生异常!{},请求参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e.getMessage(), pictureRequest);
            resp.setObjData(null);
            resp.setResultCode(WSEnum.ERROR.resultCode);
            resp.setResultInfo(WSEnum.ERROR.resultInfo);
            return resp;
        }
    }

    //聚合页广告位查询
    @PostMapping(value = "fetchAggregatePageAV")
    @ApiOperation(value = "查询聚合页广告位地址", notes = "查询特定位置的聚合页广告位")
    public Object fetchAggregatePageAV(@RequestBody PictureRequestDTO pictureRequest, HttpServletRequest request) {
        BaseResponseDTO<List<AVDTO>> resp = new BaseResponseDTO<>();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<PictureRequestDTO>> violations = validator.validate(pictureRequest);
        if (null != violations && !violations.isEmpty()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        PictureDTO pic = new PictureDTO();
        pic.setChannelCode(pictureRequest.getChannelCode());//渠道信息
        pic.setPicLocation("ACTIVITY_ZONE");//聚合页较为特殊 只查此位置的广告位
        pic.setEndTime(DateUtils.getCurrentDateTimeStr());
        pic.setType(pictureRequest.getType());
        try {
            String platFormInfo = pictureRequest.getPlatformInfo();//平台信息
            int verInt = VersionNoUtil.toVerInt(pictureRequest.getClientVersion());
            pic.setVerInt(verInt);  //当前的客户端版本
            pic.setPlatformInfo(platFormInfo);//手机操作平台信息
            pic.setEnabledMode(pictureRequest.getEnableMode());

            BaseResponseDTO<List<AVDTO>> avProviderResponse = pictureService.fetchAggregatePageAV(pic);
            if (WSEnum.SUCCESS.resultCode.equals(avProviderResponse.getResultCode())) {
                resp.setResultCode(WSEnum.SUCCESS.resultCode);
                resp.setResultInfo(WSEnum.SUCCESS.resultInfo);
                resp.setObjData(avProviderResponse.getObjData());
                resp.setChannelCode(pictureRequest.getChannelCode());
            } else {
                resp.setResultCode(avProviderResponse.getResultCode());
                resp.setResultInfo(avProviderResponse.getResultInfo());
            }
            return resp;
        } catch (Exception e) {
            log.error("方法{},发生异常!{},请求参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e.getMessage(), pictureRequest);
            resp.setObjData(null);
            resp.setResultCode(WSEnum.ERROR.resultCode);
            resp.setResultInfo(WSEnum.ERROR.resultInfo);
            return resp;
        }
    }


    @PostMapping(value = "getCustomPictureList")
    @ApiOperation(value = "app首页图片查询", notes = "依据渠道号 图片地址 平台信息 查询图片 增加版本号5.0.8的判断  ")
    public Object getCustomPictureList(@RequestBody PictureRequestDTO pictureRequest, HttpServletRequest request) {
        BaseResponseDTO resp = new BaseResponseDTO();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<PictureRequestDTO>> violations = validator.validate(pictureRequest);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        PictureDTO pic = new PictureDTO();
        pic.setChannelCode(pictureRequest.getChannelCode());
        pic.setPicLocation(pictureRequest.getPicLocation());
        pic.setEndTime(DateUtils.getCurrentDateStr());
        int applimit_50800 = 5000008;
        try {
            String platFormInfo = pictureRequest.getPlatformInfo();//平台信息
            int verInt = VersionNoUtil.toVerInt(pictureRequest.getClientVersion());
            if (StringUtil.isNullOrEmpty(pictureRequest.getPlatformInfo())) {  //5.0.8之前的版本
                platFormInfo = "All";
                verInt = 5000000;
            } else {
                if (verInt < applimit_50800) {
                    platFormInfo = "All";
                } else if (verInt >= applimit_50800) {
                    platFormInfo = pictureRequest.getPlatformInfo();
                }
            }
            pic.setPlatformInfo(platFormInfo);//手机操作平台信息
            pic.setVerInt(verInt);  //当前的客户端版本
            pic.setType(pictureRequest.getType());
            BaseResponseDTO<List<PictureDTO>> baseResponseDTO = pictureService.getValidList(pic);
            if (WSEnum.SUCCESS.resultCode.equals(baseResponseDTO.getResultCode())) {
                List<PictureDTO> pictureList = baseResponseDTO.getObjData();
                List<PicDTO> picList = new ArrayList<>();  //返回的图片集合
                if (!StringUtil.isNullOrEmpty(pictureList)) {
                    for (PictureDTO picture : pictureList) {
                        PicDTO picN = new PicDTO();
                        BeanUtils.copyNotNullProperties(picture, picN);
                        picN.setUrl(reWriteUrl(picture.getTitle(), picture.getTitleStyle(), picture.getUrl()));
                        picList.add(picN);
                    }
                }
                resp.setObjData(picList);
                resp.setChannelCode(pictureRequest.getChannelCode());
                resp.setResultCode(WSEnum.SUCCESS.resultCode);
                resp.setResultInfo(WSEnum.SUCCESS.resultInfo);
            } else {
                resp.setResultCode(baseResponseDTO.getResultCode());
                resp.setResultInfo(baseResponseDTO.getResultInfo());
            }
            return resp;
        } catch (Exception e) {
            log.error("方法{},发生异常!{},请求参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e.getMessage(), pictureRequest);
            resp.setObjData(null);
            resp.setResultCode(WSEnum.ERROR.resultCode);
            resp.setResultInfo(WSEnum.ERROR.resultInfo);
            return resp;
        }
    }

    /**
     * 重新拼接url
     *
     * @param tilte
     * @param tilteStyle
     * @param url
     * @return
     */
    private String reWriteUrl(String tilte, String tilteStyle, String url) {
        String newUrl = url;
        if (!StringUtil.isNullOrEmpty(tilteStyle) && !StringUtil.isNullOrEmpty(url)) {//重新拼接URL
            if (url.indexOf("?") > -1) {
                url += "&title=" + tilte + "&" + tilteStyle;
            } else {
                url += "?title=" + tilte + "&" + tilteStyle;
            }
            newUrl = url;
        }
        return newUrl;
    }

    //版本控制信息
    @PostMapping(value = "getVersion")
    @ApiOperation(value = "查询版本控制信息", notes = "依据 版本id 版本号 类型 等查询")
    public Object getVersion(@RequestBody VersionManageTwoDTO vmRequest, HttpServletRequest request) {

        BaseResponseDTO resp = new BaseResponseDTO();
        try {
            List<VersionManageTwoDTO> versionList = versionMangeService.getListForIndexHandle(vmRequest);
            List<VerDTO> verList = new ArrayList<>();
            if (!StringUtil.isNullOrEmpty(versionList)) {
                for (VersionManageDTO v : versionList) {
                    VerDTO ver = new VerDTO();
                    BeanUtils.copyNotNullProperties(v, ver);
                    verList.add(ver);
                }
            }
            resp.setObjData(verList);
            resp.setChannelCode(vmRequest.getChannelCode());
            resp.setResultCode(WSEnum.SUCCESS.resultCode);
            return resp;
        } catch (Exception e) {
            log.error("方法{},发生异常!{},请求参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e.getMessage(), vmRequest);
            resp.setObjData(null);
            resp.setResultCode(WSEnum.ERROR.resultCode);
            resp.setResultInfo(WSEnum.ERROR.resultInfo);
            return resp;
        }
    }


    //查询特价航线
    @PostMapping(value = "getSpecialAirLineList")
    @ApiOperation(value = "查询特价航线", notes = "根据渠道号 客户版本 查询航线信息")
    public BaseResponseDTO<List<SpecialAirlineDTO>> getSpecialAirLines(@RequestBody @Validated  BaseReq req,BindingResult bindingResult, HttpServletRequest request) {
        BaseResponseDTO<List<SpecialAirlineDTO>> resp = new BaseResponseDTO();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        List<SpecialAirlineDTO> list = specialAirLineService.getAllSpecialAirlineDTO(req.getChannelCode(), req.getClientVersion());
        if (!StringUtil.isNullOrEmpty(list)) {
            resp.setObjData(list);
            resp.setResultCode(WSEnum.SUCCESS.resultCode);
            resp.setResultInfo(WSEnum.SUCCESS.resultInfo);
        } else {
            resp.setResultCode(WSEnum.NO_DATA.resultCode);
            resp.setResultInfo(WSEnum.NO_DATA.resultInfo);
        }

        return resp;
    }


    //首页启动屏数据
    @PostMapping(value = "getAppPictureList")
    @ApiOperation(value = "获取app首页启动屏数据", notes = "依据 文件类型 查询首页图片等数据")
    public Object getAppPictures(@RequestBody BaseReq<AppPictureRequestDTO> req, HttpServletRequest request) {
        BaseResponseDTO resp = new BaseResponseDTO<>();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        try {
            AppPictureDTO appPicture = new AppPictureDTO();
            appPicture.setFileType(req.getRequest().getDeviceType().toUpperCase());
            //设置当前时间
            appPicture.setEndTime(DateUtils.getCurrentDateStr());
            //获取所有的图片信息
            List<AppPicturePartDTO> pictureList = appPictureService.getValidList(appPicture);
            //获取当前设备版本号
            String deviceVersion = req.getClientVersion();
            List<AppPicturePartDTO> accessablePictureList = new ArrayList<>();
            if (pictureList != null) {
                for (AppPicturePartDTO appPicturePart : pictureList) {
                    String minVersion = appPicturePart.getMinVersion();
                    String maxVersion = appPicturePart.getMaxVersion();
                    if (!StringUtil.isNullOrEmpty(minVersion) && !StringUtil.isNullOrEmpty(maxVersion)) {
                        //m=0,当前版本号等于最小版本号；m=1,当前版本号大于最小版本号；
                        int m = compareVersion(deviceVersion, minVersion);
                        //n=0，当前版本号等于最大版本号；n=-1，当前版本号小于最大版本号
                        int n = compareVersion(deviceVersion, maxVersion);
                        if (m >= 0 && n <= 0) {
                            accessablePictureList.add(appPicturePart);
                        }
                    }
                    //不设置最小版本则只检验最大版本
                    if (StringUtil.isNullOrEmpty(minVersion) && !StringUtil.isNullOrEmpty(maxVersion)) {
                        //n=0，当前版本号等于最大版本号；n=-1，当前版本号小于最大版本号
                        int n = compareVersion(deviceVersion, maxVersion);
                        if (n <= 0) {
                            accessablePictureList.add(appPicturePart);
                        }
                    }
                    //不设置最大版本则只检验最小版本
                    if (!StringUtil.isNullOrEmpty(minVersion) && StringUtil.isNullOrEmpty(maxVersion)) {
                        //m=0,当前版本号等于最小版本号；m=1,当前版本号大于最小版本号；
                        int m = compareVersion(deviceVersion, minVersion);
                        if (m >= 0) {
                            accessablePictureList.add(appPicturePart);
                        }
                    }
                }
            }
            List<LoadPicDTO> picList = new ArrayList<>();
            if (!StringUtil.isNullOrEmpty(accessablePictureList)) {
                for (AppPicturePartDTO appPicturePart : accessablePictureList) {
                    LoadPicDTO picN = new LoadPicDTO();
                    BeanUtils.copyNotNullProperties(appPicturePart, picN);
                    picN.setUrl(reWriteUrl(appPicturePart.getTitle(), appPicturePart.getTitleStyle(), appPicturePart.getUrl()));
                    picList.add(picN);
                }
                resp.setResultInfo(WSEnum.SUCCESS.resultInfo);
                resp.setResultCode(WSEnum.SUCCESS.resultCode);
                resp.setObjData(picList);

            } else {
                resp.setResultCode(WSEnum.NO_DATA.resultCode);
                resp.setResultInfo(WSEnum.NO_DATA.resultInfo);
            }
            return resp;
        } catch (Exception e) {
            log.error("方法{},请求参数{},发生异常!", Thread.currentThread().getStackTrace()[1].getMethodName(), req,e);
            resp.setObjData(null);
            resp.setResultCode(WSEnum.ERROR.resultCode);
            resp.setResultInfo(WSEnum.ERROR.resultInfo);
            return resp;
        }
    }

    //资源更新查询
    @PostMapping(value = "compareVer")
    @ApiOperation(value = "资源更新查询", notes = "依据平台 版本等查询 ")
    public BaseResponseDTO<List<VersionInfoDTO>> compareVer(@RequestBody BaseReq req, HttpServletRequest request) {

        BaseResponseDTO<List<VersionInfoDTO>> resp = new BaseResponseDTO<>();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        try {
            //老的APP资源更新存在问题，不返回任何数据
            if (VersionNoUtil.toVerInt(req.getClientVersion()) < 5010007) {
                resp.setResultCode(WSEnum.NO_DATA.resultCode);
                resp.setResultInfo(WSEnum.NO_DATA.resultInfo);
                return resp;
            }
            ResourceManaDTO resourceMana = new ResourceManaDTO();
            resourceMana.setPlatform(req.getPlatformInfo());
            resourceMana.setVer(req.getClientVersion());
            List<ResourceManaDTO> resList = resourceManaService.getValidList(resourceMana);
            List<VersionInfoDTO> versionInfoList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(resList)) {
                for (ResourceManaDTO res : resList) {
                    VersionInfoDTO versionInfo = new VersionInfoDTO();
                    versionInfo.setUrl(res.getUrl());
                    versionInfo.setZipMd5(res.getZipMd5());
                    versionInfo.setModuleName(res.getModuleName());
                    versionInfoList.add(versionInfo);
                }
            }
            if (!StringUtil.isNullOrEmpty(versionInfoList)) {
                resp.setObjData(versionInfoList);
                resp.setResultCode(WSEnum.SUCCESS.resultCode);
                resp.setResultInfo(WSEnum.SUCCESS.resultInfo);
            } else {
                resp.setResultCode(WSEnum.NO_DATA.resultCode);
                resp.setResultInfo(WSEnum.NO_DATA.resultInfo);
            }

            return resp;
        } catch (Exception e) {
            log.error("方法{},发生异常!{},请求参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e.getMessage(), req);
            resp.setObjData(null);
            resp.setResultCode(WSEnum.ERROR.resultCode);
            resp.setResultInfo(WSEnum.ERROR.resultInfo);
            return resp;
        }
    }

    //查询app版本信息
    @PostMapping(value = "getAppVersion")
    @ApiOperation(value = "查询app版本信息", notes = "依据设备名称和版本号查询")
    public Object getAppVersion(@RequestBody BaseReq req, HttpServletRequest request) {
        BaseResponseDTO<AppVersionManageDTO> resp = new BaseResponseDTO<>();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        try {
            //根据设备名称和版本号查询强制更新标记
            AppVersionManageDTO appVersionManage = new AppVersionManageDTO();
            appVersionManage.setDeviceType(req.getPlatformInfo().toLowerCase());
            appVersionManage.setAppVersionNo(req.getClientVersion().toLowerCase());
            //查询当前版本信息
            AppVersionManageDTO appVersionManageDTO = appVersionManageService.queryAppForceUpdate(appVersionManage);
            //根据传入的设备信息,查询更新描述以及最大版本号
            AppVersionManageDTO appVersionManages = appVersionManageService.queryForceUpdateAndMaxVersionNo(appVersionManage);
            if (appVersionManages == null) {
                appVersionManages = new AppVersionManageDTO();
                resp.setResultInfo("已是最新版本");
            }
            appVersionManages.setDeviceType(req.getPlatformInfo().toLowerCase());
            if (null != appVersionManageDTO) {
                appVersionManages.setForceUpdate(StringUtil.isNullOrEmpty(appVersionManageDTO.getForceUpdate()) ? "N" : appVersionManageDTO.getForceUpdate());
                appVersionManages.setLoginFlag(appVersionManageDTO.getLoginFlag());
                appVersionManages.setVersionTimestamp(appVersionManageDTO.getVersionTimestamp());
                appVersionManages.setThirdPartyLoginFlag(StringUtils.isBlank(appVersionManageDTO.getThirdPartyLoginFlag()) ? "N" : appVersionManageDTO.getThirdPartyLoginFlag());
            }
            resp.setObjData(appVersionManages);
            resp.setResultCode(WSEnum.SUCCESS.resultCode);
            return resp;
        } catch (Exception e) {
            log.error("方法{},发生异常!{},请求参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e.getMessage(), req);
            resp.setObjData(null);
            resp.setResultCode(WSEnum.ERROR.resultCode);
            resp.setResultInfo(WSEnum.ERROR.resultInfo);
            return resp;
        }
    }

    /**
     * 比较版本号的大小,前者大则返回一个正数,后者大返回一个负数,相等则返回0
     *
     * @param version1 当前设备版本
     * @param version2 需要比较的版本
     * @return
     */
    private static int compareVersion(String version1, String version2) throws Exception {
        if (version1 == null || version2 == null) {
            throw new Exception("compareVersion error:illegal params.");
        }
        //注意此处为正则匹配，不能用"."；
        String[] versionArray1 = version1.split("\\.");
        String[] versionArray2 = version2.split("\\.");
        int idx = 0;
        //取最小长度值
        int minLength = Math.min(versionArray1.length, versionArray2.length);
        int diff = 0;
        while (idx < minLength
                //先比较长度
                && (diff = versionArray1[idx].length() - versionArray2[idx].length()) == 0
                //再比较字符
                && (diff = versionArray1[idx].compareTo(versionArray2[idx])) == 0) {
            ++idx;
        }
        //如果已经分出大小，则直接返回，如果未分出大小，则再比较位数，有子版本的为大；
        diff = (diff != 0) ? diff : versionArray1.length - versionArray2.length;
        return diff;
    }

    /**
     * 获取服务信息
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/getModulars")
    @ApiOperation(value = "获取模块服务信息", notes = "渠道号，平台信息，版本号等 查询")
    public BaseResponseDTO getModulars(@RequestBody BaseReq req, HttpServletRequest request) {

        BaseResponseDTO result = new BaseResponseDTO();
        try {
            result = modularVersionManageService.getModulars(req);
        } catch (Exception e) {
            log.error("方法{},发生异常!{},请求参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e.getMessage(), req);
            result.setResultCode(WSEnum.ERROR.resultCode);
            result.setResultInfo(WSEnum.ERROR.resultInfo);
        }

        return result;
    }

    /**
     * @Description 协议 条款信息 获取
     * @Param: No such property: code for class: Script1
     * @return:
     * @Author: zhangwanli
     * @Date: 2019/8/28
     */
    @PostMapping(value = "/getAllNoticeInfo")
    @ApiOperation(value = "获取协议 条款信息", notes = "依据父类模块id 等获取")
    public BaseResponseDTO getAllNoticeInfo(@RequestBody BaseReq<NoticeRequestDTO> req, HttpServletRequest request) {

        BaseResponseDTO result = new BaseResponseDTO();
        try {
            result = noticeService.getAllNoticeInfo(req);
        } catch (Exception e) {
            log.error("方法{},发生异常!{},请求参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e.getMessage(), req);
            result.setResultInfo(WSEnum.ERROR.resultInfo);
            result.setResultCode(WSEnum.ERROR.resultCode);
        }
        return result;
    }

    /**
     * @Description 获取富文本信息
     * @Param: No such property: code for class: Script1
     * @return:
     * @Author: zhangwanli
     * @Date: 2019/8/28
     */
    @PostMapping(value = "/getRichTextNoticeInfo")
    @ApiOperation(value = "获取协议 条款信息", notes = "依据父类模块id 等获取")
    public BaseResponseDTO getRichTextNoticeInfo(@RequestBody BaseReq<NoticeRequestDTO> req) {

        BaseResponseDTO result = new BaseResponseDTO();
        try {
            result = noticeService.getRichTextNoticeInfo(req);
        } catch (Exception e) {
            log.error("方法{},发生异常!{},请求参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e.getMessage(), req);
            result.setResultInfo(WSEnum.ERROR.resultInfo);
            result.setResultCode(WSEnum.ERROR.resultCode);
        }
        return result;
    }

    //查询支付宝绑定吉祥会员列表
    @PostMapping(value = "/searchAliBindingList")
    @ApiOperation(value = "查询支付宝绑定吉祥会员列表", notes = "查询支付宝绑定吉祥会员列表")
    public BaseResultDTO<List<AliBindingDTO>> searchAliBindingList(@RequestBody @Validated BaseReq<AliBindingDTO> req) {
        return aliBindingService.searchAliBindingList(req);
    }

    //保存支付宝绑定吉祥会员关系
    @PostMapping(value = "/saveAliBindingRelationship")
    @ApiOperation(value = "保存支付宝绑定吉祥会员关系", notes = "保存支付宝绑定吉祥会员关系")
    public BaseResultDTO<Object> saveAliBindingRelationship(@RequestBody @Validated BaseReq<AliBindingDTO> req) {
        return aliBindingService.saveAliBindingRelationship(req);
    }

    //解绑支付宝绑定吉祥会员关系
    @PostMapping(value = "/unBindAliBindingRelationship")
    @ApiOperation(value = "解绑支付宝绑定吉祥会员关系", notes = "解绑支付宝绑定吉祥会员关系")
    public BaseResultDTO<Object> unBindAliBindingRelationship(@RequestBody @Validated BaseReq<AliBindingDTO> req) {
        return aliBindingService.unBindAliBindingRelationship(req);
    }

    @RequestMapping("getNavigation")
    @ApiOperation(value = "查询App底部导航栏", notes = "依据渠道号  平台信息 版本号等查询底部导航栏")
    public BaseResponseDTO getNavigation(@RequestBody BaseReq req) {
        BaseResponseDTO baseResponseDTO = new BaseResponseDTO();
        try {
            baseResponseDTO = navigationService.getNavigation(req);
        } catch (Exception e) {
            log.error("方法{},发生异常!{}，参数{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e,req);
            baseResponseDTO.setResultCode(WSEnum.ERROR.resultCode);
            baseResponseDTO.setResultInfo(WSEnum.ERROR.resultInfo);
        }
        return baseResponseDTO;
    }
}
