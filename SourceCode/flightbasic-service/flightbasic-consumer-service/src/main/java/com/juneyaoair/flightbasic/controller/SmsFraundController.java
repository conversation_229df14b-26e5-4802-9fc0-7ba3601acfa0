package com.juneyaoair.flightbasic.controller;

import com.juneyaoair.flightbasic.commondto.RequestData;
import com.juneyaoair.flightbasic.commondto.ResponseData;
import com.juneyaoair.flightbasic.request.TravellerCtcm;
import com.juneyaoair.flightbasic.service.impl.SendSmsFraundService;
import com.juneyaoair.utils.util.ObjCheckUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/5 13:43
 */
@Api(value = "SmsFraudController",tags = "防诈提醒名单控制类")
@RestController
public class SmsFraundController {
    @Autowired
    private SendSmsFraundService sendSmsFraundService;
    @ApiOperation(value = "插入防诈提醒名单")
    @PostMapping(value = "recordSmsFraudRecord")
    public ResponseData recordSmsFraudRecord(@RequestBody @Validated RequestData<TravellerCtcm> requestData){
        TravellerCtcm travellerCtcm = requestData.getData();
        ObjCheckUtil.isNull(travellerCtcm,"请求参数缺失");
        sendSmsFraundService.insertRecord(travellerCtcm.getSegmentDepaDate(),travellerCtcm.getTravellerPhoneCtcm());
        return ResponseData.success();
    }

    @ApiOperation(value = "发送防诈提醒名单")
    @PostMapping(value = "sendSms")
    public ResponseData sendSms(@RequestBody @Validated RequestData<String> requestData){
        sendSmsFraundService.sendSms(requestData.getData());
        return ResponseData.success();
    }
}
