package com.juneyaoair.flightbasic.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/17  10:26.
 */
@Data
@Component
public class AntifraudConfig {
    /**
     * IP地址白名单
     */
    @Value("#{'${tongdun.ipWhiteList:}'.empty ? null : '${tongdun.ipWhiteList:}'.split(',')}")
    private List<String> ipWhiteList;
    /**
     * 最小会员等级
     * 高于此等级的会员不进行同盾拦截
     */
    @Value("${tongdun.minMemberLevel:4}")
    private int minMemberLevel;

    /**
     * 同盾访问地址
     */
    @Value("${tongdun.api.url}")
    private String apiUrl;
    /**
     * ip地址池
     */
    @Value("#{'${tongdun.ipPool:}'.empty ? null : '${tongdun.ipPool:}'.split(',')}")
    private List<String> ipPool;

}
