package com.juneyaoair.flightbasic.controller;

import com.juneyaoair.flightbasic.common.BaseReq;
import com.juneyaoair.flightbasic.common.BaseRespDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.response.paynote.PayMethodOutDTO;
import com.juneyaoair.flightbasic.service.paynote.IPayNoteService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @description 支付文案配置
 * @date 2020/2/28  14:00.
 */
@Slf4j
@RestController
@RequestMapping("notes")
public class PayNoteController {
    @Autowired
    private IPayNoteService payNoteService;

    @ApiOperation(value = "查询支付文案配置", notes = "查询支付文案配置")
    @PostMapping("queryValidPayNotes")
    public BaseRespDTO<List<PayMethodOutDTO>> queryValidPayNotes(@RequestBody @Validated BaseReq req, BindingResult validResult, HttpServletRequest request){
        BaseRespDTO baseResultDTO = new BaseRespDTO();
        if (validResult.hasErrors()) {
            String strMess = validResult.getAllErrors().get(0).getDefaultMessage();
            baseResultDTO.setResultInfo(strMess);
            baseResultDTO.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.resultCode);
            return baseResultDTO;
        }
        try{
            List<PayMethodOutDTO> payMethodOutDTOList = payNoteService.queryValidNotes(req);
            baseResultDTO.setResultCode(WSEnum.SUCCESS.resultCode);
            baseResultDTO.setResultInfo(WSEnum.SUCCESS.resultInfo);
            baseResultDTO.setObjData(payMethodOutDTOList);
        }catch (Exception e){
            log.error("方法{},发生异常!{}", Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            baseResultDTO.setResultCode(WSEnum.ERROR.resultCode);
            baseResultDTO.setResultInfo(WSEnum.ERROR.resultInfo);
        }
        return baseResultDTO;
    }
}
