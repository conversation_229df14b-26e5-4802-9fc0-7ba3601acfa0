package com.juneyaoair.flightbasic.service.basic.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.appenum.AddonRemarkEnum;
import com.juneyaoair.flightbasic.comm.ThemeFlightEnum;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.config.FlightConfig;
import com.juneyaoair.flightbasic.feign.basic.*;
import com.juneyaoair.flightbasic.flightservice.IGenshinFlightService;
import com.juneyaoair.flightbasic.inner.component.LocalCacheComponent;
import com.juneyaoair.flightbasic.inner.service.BasicDataService;
import com.juneyaoair.flightbasic.mysql.model.GenshinFlightInfoPo;
import com.juneyaoair.flightbasic.redis.FlightBasicRedisUtil;
import com.juneyaoair.flightbasic.redis.RedisKeyConstants;
import com.juneyaoair.flightbasic.request.airLine.AirLineReqDTO;
import com.juneyaoair.flightbasic.request.airport.AirPortInfoReqDTO;
import com.juneyaoair.flightbasic.request.city.CityInfoReqDTO;
import com.juneyaoair.flightbasic.request.commonFlyCity.CommonFlyCityReqDTO;
import com.juneyaoair.flightbasic.request.cooperationAirLine.CooperationAirlineReqDTO;
import com.juneyaoair.flightbasic.request.country.TCountryReqDTO;
import com.juneyaoair.flightbasic.request.flightInfo.FlightInfoReqDTO;
import com.juneyaoair.flightbasic.request.flightInfo.ThemeFlightParam;
import com.juneyaoair.flightbasic.request.flightTicketPrice.FlightTicketPriceByYDTO;
import com.juneyaoair.flightbasic.request.province.ProvinceReqDTO;
import com.juneyaoair.flightbasic.request.trrFlightInfo.ParamTrrFlightInfo;
import com.juneyaoair.flightbasic.response.airline.AirLineDTO;
import com.juneyaoair.flightbasic.response.airline.ThemeAirline;
import com.juneyaoair.flightbasic.response.airport.AirPortInfoDTO;
import com.juneyaoair.flightbasic.response.city.CityInfoDTO;
import com.juneyaoair.flightbasic.response.cooperationAirLine.CooperationAirlineDTO;
import com.juneyaoair.flightbasic.response.country.TCountryDTO;
import com.juneyaoair.flightbasic.response.flightInfo.FlightInfoDTO;
import com.juneyaoair.flightbasic.response.flightPrice.FlightTicketPriceByYResp;
import com.juneyaoair.flightbasic.response.province.ProvinceDTO;
import com.juneyaoair.flightbasic.response.trrFlightInfo.TrrFlightInfoDTO;
import com.juneyaoair.flightbasic.service.CommonService;
import com.juneyaoair.flightbasic.service.basic.FlightBasicService;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class FlightBasicServiceImpl extends CommonService implements FlightBasicService {
    @Autowired
    private FeignCountryService feignCountryService;
    @Autowired
    private FeignProvinceService feignProvinceService;
    @Autowired
    private FeignCityInfoService feignCityInfoService;
    @Autowired
    private FeignAirLineService feignAirLineService;
    @Resource
    private FlightBasicRedisUtil redisUtil;
    @Autowired
    private FeignCooperationAirLineService cooperationAirlineService;
    @Autowired
    private FeignFlightInfoService feignFlightInfoService;
    @Autowired
    private FeignCommonflyCityService feignCommonflyCityService;
    @Autowired
    private BasicDataService basicDataService;

    @Autowired
    private LocalCacheComponent codesLocalCache;

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Autowired
    private IGenshinFlightService genshinFlightService;
    @Autowired
    private FlightConfig flightConfig;

    @Value("${airline.version:V2}")
    private String airlineVersion;


    private final String THEME = ThemeFlightEnum.LEGO.getThemeName();

    @Override
    public BaseResultDTO<List<TCountryDTO>> searchCountry(BaseRequestDTO<TCountryReqDTO> requestDTO) {
        List<TCountryDTO> countryDTOS = new ArrayList<>();
        BaseResultDTO<List<TCountryDTO>> resultDTO = new BaseResultDTO<>();
        try {
            if (!redisUtil.exists(RedisKeyConstants.BASIC_COUNTRY)) {
                //通过feign获得结果集
                resultDTO = feignCountryService.countryInfo(requestDTO);
                //缓存所有记录
                requestDTO.setRequest(new TCountryReqDTO());
                BaseResultDTO<List<TCountryDTO>> allResultDTO = feignCountryService.countryInfo(requestDTO);
                if (null != allResultDTO && allResultDTO.getResultCode().equals("10001")) {
                    redisUtil.set(RedisKeyConstants.BASIC_COUNTRY, JsonUtil.objectToJson(allResultDTO.getResult()), 600L);
                }
                return resultDTO;
            } else {
                //如果redis中有缓存,取出并根据请求参数过滤redis数据
                String redisResult = redisUtil.get(RedisKeyConstants.BASIC_COUNTRY);
                if (StringUtils.isNotBlank(redisResult)) {
                    countryDTOS = (List<TCountryDTO>) JsonUtil.jsonToBean(redisResult, new TypeToken<List<TCountryDTO>>() {
                    }.getType());
                    //获取处请求参数用于过滤redis结果集
                    TCountryReqDTO countryReqDTO = requestDTO.getRequest();
                    if (null != countryReqDTO) {
                        if (StringUtils.isNotBlank(countryReqDTO.getCountryCode())) {
                            countryDTOS = countryDTOS.stream().filter(i -> i.getCountryCode().equals(countryReqDTO.getCountryCode())).collect(Collectors.toList());
                        }
                        if (StringUtils.isNotBlank(countryReqDTO.getCountryName())) {
                            countryDTOS = countryDTOS.stream().filter(i -> i.getCountryName().equals(countryReqDTO.getCountryName())).collect(Collectors.toList());
                        }
                        if (StringUtils.isNotBlank(countryReqDTO.getEnglishName())) {
                            countryDTOS = countryDTOS.stream().filter(i -> i.getEnglishName().equals(countryReqDTO.getEnglishName())).collect(Collectors.toList());
                        }
                        if (StringUtils.isNotBlank(countryReqDTO.getHotCountry())) {
                            countryDTOS = countryDTOS.stream().filter(i -> i.getHotCountry().equals(countryReqDTO.getHotCountry())).collect(Collectors.toList());
                        }
                        if (StringUtils.isNotBlank(countryReqDTO.getRegionCode())) {
                            countryDTOS = countryDTOS.stream().filter(i -> i.getRegionCode().equals(countryReqDTO.getRegionCode())).collect(Collectors.toList());
                        }
                    }
                }
                resultDTO.setResult(countryDTOS);
                resultDTO.setResultCode(WSEnum.SUCCESS.resultCode);
            }
        } catch (Exception e) {
            log.error("查询国家信息出错！", e);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
        }
        return resultDTO;
    }

    @Override
    public BaseResultDTO<List<ProvinceDTO>> searchProvince(BaseRequestDTO<ProvinceReqDTO> requestDTO) {
        BaseResultDTO<List<ProvinceDTO>> resultDTO = new BaseResultDTO<>();
        ProvinceReqDTO requestParam = requestDTO.getRequest();
        List<ProvinceDTO> provinceDTOList = new ArrayList<>();
        try {
            //判断redis有无数据：如果redis没有数据，则进行缓存并返回结果。否则直接从redis中取数据然后根据请求参数过滤数据并返回结果
            if (!redisUtil.exists(RedisKeyConstants.BASIC_PROVINCE)) {
                resultDTO = feignProvinceService.getAllProvince(requestDTO);
                requestDTO.setRequest(new ProvinceReqDTO());
                BaseResultDTO<List<ProvinceDTO>> allResultDTO = feignProvinceService.getAllProvince(requestDTO);
                if (null != allResultDTO && allResultDTO.getResultCode().equals("10001")) {
                    redisUtil.set(RedisKeyConstants.BASIC_PROVINCE, JsonUtil.objectToJson(allResultDTO.getResult()), 600L);
                }
                return resultDTO;
            } else {
                //如果redis中有缓存,取出并根据请求参数过滤redis数据
                String redisResult = redisUtil.get(RedisKeyConstants.BASIC_PROVINCE);
                if (StringUtils.isNotBlank(redisResult)) {
                    provinceDTOList = (List<ProvinceDTO>) JsonUtil.jsonToBean(redisResult, new TypeToken<List<ProvinceDTO>>() {
                    }.getType());
                    if (null != requestParam) {
                        //获取请求参数用于过滤redis数据
                        if (StringUtils.isNotBlank(requestParam.getCountryCode())) {
                            provinceDTOList = provinceDTOList.stream().filter(i -> i.getCountryCode().equals(requestParam.getCountryCode())).collect(Collectors.toList());
                        }
                        if (StringUtils.isNotBlank(requestParam.getCountryName())) {
                            provinceDTOList = provinceDTOList.stream().filter(i -> i.getCountryName().equals(requestParam.getCountryName())).collect(Collectors.toList());
                        }
                        if (null != requestParam.getProvinceId()) {
                            provinceDTOList = provinceDTOList.stream().filter(i -> i.getProvinceId().equals(requestParam.getProvinceId())).collect(Collectors.toList());
                        }
                        if (StringUtils.isNotBlank(requestParam.getProvinceName())) {
                            provinceDTOList = provinceDTOList.stream().filter(i -> i.getProvinceName().equals(requestParam.getProvinceName())).collect(Collectors.toList());
                        }
                    }
                }
                resultDTO.setResult(provinceDTOList);
                resultDTO.setResultCode(WSEnum.SUCCESS.resultCode);
            }
        } catch (Exception e) {
            log.error("查询省份信息出错！{}", e);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
        }
        return resultDTO;
    }

    @Override
    public BaseResultDTO<List<AirPortInfoDTO>> searchAirport(BaseRequestDTO<AirPortInfoReqDTO> requestDTO) {
        return searchAirportV2(requestDTO);
    }

    @Override
    public BaseResultDTO<List<AirPortInfoDTO>> searchAirportV2(BaseRequestDTO<AirPortInfoReqDTO> requestDTO) {
        BaseResultDTO<List<AirPortInfoDTO>> resultDTO = new BaseResultDTO<>();
        AirPortInfoReqDTO requestParam = requestDTO.getRequest();
        List<AirPortInfoDTO> airPortInfoDTOList = basicDataService.searchAirport(requestParam);
        resultDTO.setResult(airPortInfoDTOList);
        resultDTO.success();
        return resultDTO;
    }

    @Override
    public BaseResultDTO<List<CityInfoDTO>> searchCityInfo(BaseRequestDTO<CityInfoReqDTO> requestDTO) {
        return searchCityInfoV2(requestDTO);
    }

    @Override
    public BaseResultDTO<List<CityInfoDTO>> searchCityInfoV2(BaseRequestDTO<CityInfoReqDTO> requestDTO) {
        BaseResultDTO<List<CityInfoDTO>> resultDTO = new BaseResultDTO<>();
        CityInfoReqDTO requestParam = requestDTO.getRequest();
        List<CityInfoDTO> cityInfoDTOList;
        try {
            //判断redis有无数据：如果redis没有数据，则进行缓存并返回结果。否则直接从redis中取数据然后根据请求参数过滤数据并返回结果
            cityInfoDTOList = codesLocalCache.getLocalOrRedisHashAll(RedisKeyConstants.BASIC_CITYINFO_HASH, new TypeToken<CityInfoDTO>() {
            }.getType());
            if (CollectionUtils.isEmpty(cityInfoDTOList)) {
                //1.查询数据库,查全部
                requestDTO.setRequest(new CityInfoReqDTO());
                resultDTO = feignCityInfoService.getAllCityInfosV2(requestDTO);
                if (null != resultDTO && WSEnum.SUCCESS.resultCode.equals(resultDTO.getResultCode()) && CollectionUtils.isNotEmpty(resultDTO.getResult())) {
                    cityInfoDTOList = resultDTO.getResult();
                    try {
                        String cacheKey = RedisKeyConstants.BASIC_CITYINFO_HASH;
                        codesLocalCache.putCityList(cacheKey, cityInfoDTOList);
                        Map<String, String> airPortInfoDTOMap = cityInfoDTOList.stream().collect(Collectors.toMap(CityInfoDTO::getCityCode, obj -> JsonUtil.objectToJson(obj)));
                        //默认存放10分钟
                        redisUtil.putHashAll(cacheKey, airPortInfoDTOMap, 60 * 10L);
                    } catch (Exception e) {
                        log.error("城市数据:{},缓存处理异常:", JsonUtil.objectToJson(resultDTO), e);
                    }
                } else {
                    resultDTO.fail(WSEnum.ERROR.resultCode, Objects.isNull(resultDTO) ? WSEnum.ERROR.resultInfo : resultDTO.getErrorMsg());
                    return resultDTO;
                }
            }
            if (requestParam != null) {
                //处理接下来的数据
                if (StringUtils.isNotBlank(requestParam.getCityCode())) {
                    cityInfoDTOList = cityInfoDTOList.stream().filter(i -> requestParam.getCityCode().equals(i.getCityCode())).collect(Collectors.toList());
                }
                if (StringUtils.isNotBlank(requestParam.getCountryCode())) {
                    cityInfoDTOList = cityInfoDTOList.stream().filter(i -> requestParam.getCountryCode().equals(i.getCountryCode())).collect(Collectors.toList());
                }
            }
            resultDTO.setResult(cityInfoDTOList);
            resultDTO.success();
        } catch (Exception e) {
            log.error("查询城市信息出错！", e);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
        }
        return resultDTO;
    }

    /**
     * 默认使用V2版本
     *
     * @param requestDTO
     * @return
     */
    @Override
    public BaseResultDTO<List<AirLineDTO>> searchAirLine(BaseRequestDTO<AirLineReqDTO> requestDTO) {
     /*   if ("V1".equals(airlineVersion)) {
            return searchAirLineV1(requestDTO);
        } else {
            return searchAirLineV2(requestDTO);
        }*/
        return searchAirLineV2(requestDTO);
    }

/*    @Override
    public BaseResultDTO<List<AirLineDTO>> searchAirLineV1(BaseRequestDTO<AirLineReqDTO> requestDTO) {
        BaseResultDTO<List<AirLineDTO>> resultDTO = new BaseResultDTO<>();
        AirLineReqDTO requestParam = requestDTO.getRequest();
        try {
            //判断redis有无数据：如果redis没有数据，则进行缓存并返回结果。否则直接从redis中取数据然后根据请求参数过滤数据并返回结果
            if (!redisUtil.exists(RedisKeyConstants.BASIC_AIRLINE)) {
                resultDTO = feignAirLineService.getAllAirLine(requestDTO);
                requestDTO.setRequest(new AirLineReqDTO());
                BaseResultDTO<List<AirLineDTO>> allResultDTO = feignAirLineService.getAllAirLine(requestDTO);
                if (null != allResultDTO && WSEnum.SUCCESS.resultCode.equals(allResultDTO.getResultCode())) {
                    redisUtil.set(RedisKeyConstants.BASIC_AIRLINE, JsonUtil.objectToJson(allResultDTO.getResult()), 600L);
                    codesLocalCache.putAirLineList(allResultDTO.getResult());
                }
                return resultDTO;
            }

            //如果redis中有缓存,取出并根据请求参数过滤redis数据
            List<AirLineDTO> airLineDTOList = codesLocalCache.getAirlineList();
            if (Objects.nonNull(airLineDTOList) && !airLineDTOList.isEmpty()) {
                if (null != requestParam) {
                    Stream<AirLineDTO> stream = airLineDTOList.stream();
                    //获取请求参数用于过滤redis数据
                    stream = StringUtils.isNotBlank(requestParam.getDepAirport()) ? stream.filter(i -> requestParam.getDepAirport().equals(i.getDepAirport())) : stream;
                    stream = StringUtils.isNotBlank(requestParam.getArrAirport()) ? stream.filter(i -> requestParam.getArrAirport().equals(i.getArrAirport())) : stream;
                    stream = StringUtils.isNotBlank(requestParam.getDepCity()) ? stream.filter(i -> requestParam.getDepCity().equals(i.getDepCity())) : stream;
                    stream = StringUtils.isNotBlank(requestParam.getArrCity()) ? stream.filter(i -> requestParam.getArrCity().equals(i.getArrCity())) : stream;
                    stream = StringUtils.isNotBlank(requestParam.getIsTransit()) ? stream.filter(i -> requestParam.getIsTransit().equals(i.getIsTransit())) : stream;
                    stream = StringUtils.isNotBlank(requestParam.getCarrierCompany()) ? stream.filter(i -> requestParam.getCarrierCompany().equals(i.getCarrierCompany())) : stream;
                    stream = StringUtils.isNotBlank(requestParam.getIsInternationalAirline()) ? stream.filter(i -> requestParam.getIsInternationalAirline().equals(i.getIsInternationalAirline())) : stream;
                    stream = StringUtils.isNotBlank(requestParam.getAddonRemark()) ? stream.filter(i -> i.getAddonRemark().equals(requestParam.getAddonRemark())) : stream;
                    airLineDTOList = stream.collect(Collectors.toList());
                }
            }
            resultDTO.setResult(airLineDTOList);
            resultDTO.setResultCode(WSEnum.SUCCESS.resultCode);
        } catch (Exception e) {
            log.error("查询航线信息出错！", e);
            resultDTO.setErrorMsg(WSEnum.ERROR.resultInfo);
            resultDTO.setResultCode(WSEnum.ERROR.resultCode);
        }
        return resultDTO;
    }*/

    @Override
    public BaseResultDTO<List<AirLineDTO>> searchAirLineV2(BaseRequestDTO<AirLineReqDTO> requestDTO) {
        BaseResultDTO<List<AirLineDTO>> resultDTO = new BaseResultDTO<>();
        AirLineReqDTO requestParam = requestDTO.getRequest();
        //***一.获取状态值***
        //1.通过枚举获取所有状态值,或通过枚举来获取
        List<String> addonRemarkList = AddonRemarkEnum.getAllCode();
        List<AirLineDTO> allAirLineDTOList = new ArrayList<>();
        //2.判断值状态条件值是否存在在状态值中，如果没有则返回错误。将对应结果封装到集合中。
        if (requestParam != null ) {
            if(StringUtils.isNotBlank(requestParam.getAddonRemark())){
                addonRemarkList = addonRemarkList.stream().filter(str -> str.equals(requestParam.getAddonRemark())).collect(Collectors.toList());
                //判断为空，代表枚举未配置,返回错误信息
                if (addonRemarkList.isEmpty()) {
                    log.error("查询航线信息，请求参数错误，addonRemark未在枚举中配置！addonRemark参数{}，枚举配置内容:{}", requestParam.getAddonRemark(), AddonRemarkEnum.getAllCode());
                    resultDTO.fail(WSEnum.ERROR.resultCode, StringUtil.formateString("请求参数错误，addonRemark未在枚举中配置！addonRemark参数{}，枚举配置内容", requestParam.getAddonRemark(), addonRemarkList.toString()));
                    return resultDTO;
                }
            }
            //指定了航线对，通过HGet方式获取
            if (StringUtils.isNotBlank(requestParam.getDepCity()) && StringUtils.isNotBlank(requestParam.getArrCity())) {
                for (String addonRemark : addonRemarkList) {
                    String redisK = RedisKeyConstants.BASIC_AIRLINE_ADDON_REMARK + addonRemark;
                    List<AirLineDTO> airLineDTOList = queryByDepCityAndArrCity(redisK, requestParam.getDepCity(), requestParam.getArrCity());
                    if (CollectionUtils.isNotEmpty(airLineDTOList)) {
                        allAirLineDTOList.addAll(airLineDTOList);
                    }
                }
            }
        }
        if(CollectionUtils.isEmpty(allAirLineDTOList)){
            //非指定航线或无缓存数据，通过全量数据进行筛选
            List<Future<List<AirLineDTO>>> futureList = new ArrayList<>(addonRemarkList.size());
            //***二.执行查询****
            //1.循环AddonRemarkList数据
            for (String addonRemark : addonRemarkList) {
                //利用多线程异步执行查询操作
                Callable<List<AirLineDTO>> callable = () -> {
                    //2.判断对应k是否存在
                    String redisK = RedisKeyConstants.BASIC_AIRLINE_ADDON_REMARK + addonRemark;
                    List<AirLineDTO> airLineDTOList = codesLocalCache.getLocalOrRedisHashArray(redisK, new TypeToken<List<AirLineDTO>>() {
                    }.getType());
                    //缓存中无航线数据，需要重新拉取设置缓存信息
                    if (CollectionUtils.isEmpty(airLineDTOList)) {
                        //不存在则查询数据库，并设置缓存
                        airLineDTOList = cacheRouteData(addonRemark, redisK, requestDTO);
                    }
                    return airLineDTOList;
                };
                Future<List<AirLineDTO>> future = threadPoolTaskExecutor.submit(callable);
                futureList.add(future);
            }
            if (CollectionUtils.isNotEmpty(futureList)) {
                //String id = StringUtil.new6GUID();
                //StopWatch stopWatch =new StopWatch("searchAirLineV2多线程处理"+id);
                //循环遍历获取结果
                for (Future<List<AirLineDTO>> future : futureList) {
                    //stopWatch.start(id+future.toString());
                    try {
                        List<AirLineDTO> airLineDTOList = future.get(10L, TimeUnit.SECONDS);
                        if (CollectionUtils.isNotEmpty(airLineDTOList)) {
                            allAirLineDTOList.addAll(airLineDTOList);
                        }
                    } catch (InterruptedException e) {
                        log.error("InterruptedException:", e);
                    } catch (ExecutionException e) {
                        log.error("ExecutionException:", e);
                    } catch (TimeoutException e) {
                        log.error("TimeoutException:", e);
                    }
                    //stopWatch.stop();
                }
                //log.info("{},searchAirLineV2多线程处理处理耗时:{}",id, stopWatch.prettyPrint());
            }
        }
        //获取请求参数用于过滤数据
        if (null != requestParam) {
            //****处理返回结果****
            Stream<AirLineDTO> stream = allAirLineDTOList.stream();
            stream = StringUtils.isNotBlank(requestParam.getDepAirport()) ? stream.filter(i -> requestParam.getDepAirport().equals(i.getDepAirport())) : stream;
            stream = StringUtils.isNotBlank(requestParam.getArrAirport()) ? stream.filter(i -> requestParam.getArrAirport().equals(i.getArrAirport())) : stream;
            stream = StringUtils.isNotBlank(requestParam.getDepCity()) ? stream.filter(i -> requestParam.getDepCity().equals(i.getDepCity())) : stream;
            stream = StringUtils.isNotBlank(requestParam.getArrCity()) ? stream.filter(i -> requestParam.getArrCity().equals(i.getArrCity())) : stream;
            stream = StringUtils.isNotBlank(requestParam.getIsTransit()) ? stream.filter(i -> requestParam.getIsTransit().equals(i.getIsTransit())) : stream;
            stream = StringUtils.isNotBlank(requestParam.getCarrierCompany()) ? stream.filter(i -> requestParam.getCarrierCompany().equals(i.getCarrierCompany())) : stream;
            stream = StringUtils.isNotBlank(requestParam.getIsInternationalAirline()) ? stream.filter(i -> requestParam.getIsInternationalAirline().equals(i.getIsInternationalAirline())) : stream;
            stream = StringUtils.isNotBlank(requestParam.getAddonRemark()) ? stream.filter(i -> i.getAddonRemark().equals(requestParam.getAddonRemark())) : stream;
            allAirLineDTOList = stream.collect(Collectors.toList());
        }
        resultDTO.setResult(allAirLineDTOList);
        resultDTO.success();
        return resultDTO;
    }

    //按照指定航线模式查询航线
    private List<AirLineDTO> queryByDepCityAndArrCity(String redisK, String depCity, String arrCity) {
        //先从本地缓存获取
        String localCacheKey = redisK+depCity+arrCity;
        List<AirLineDTO> airLineDTOList = codesLocalCache.getLocal(localCacheKey);
        if (CollectionUtils.isEmpty(airLineDTOList)) {
            //其次从redis获取，并更新至本地缓存
            String airlineStr = redisUtil.getHash(redisK, depCity + arrCity);
            if (StringUtils.isNotBlank(airlineStr)) {
                airLineDTOList = JsonUtil.fromJson(airlineStr, new TypeToken<List<AirLineDTO>>() {
                }.getType());
                //设置本地缓存
                codesLocalCache.putAirLineList(localCacheKey,airLineDTOList);
            }
        }
        return airLineDTOList;
    }

    /**
     * 查询全量航线数据进行缓存
     *
     * @param addonRemark
     * @param redisK
     * @param requestDTO
     * @return
     */
    private List<AirLineDTO> cacheRouteData(String addonRemark, String redisK, BaseRequestDTO<AirLineReqDTO> requestDTO) {
        BaseRequestDTO baseRequestDTO = new BaseRequestDTO();
        BeanUtils.copyNotNullProperties(requestDTO, baseRequestDTO);
        AirLineReqDTO airLineReqDTO = new AirLineReqDTO();
        airLineReqDTO.setAddonRemark(addonRemark);
        baseRequestDTO.setRequest(airLineReqDTO);
        BaseResultDTO<List<AirLineDTO>> allResultDTO = feignAirLineService.getAllAirLine(baseRequestDTO);
        List<AirLineDTO> airLineDTOList = new ArrayList<>();
        if (allResultDTO != null && WSEnum.SUCCESS.resultCode.equals(allResultDTO.getResultCode()) && CollectionUtils.isNotEmpty(allResultDTO.getResult())) {
            airLineDTOList = allResultDTO.getResult();
            try {
                //设置本地缓存
                codesLocalCache.putAirLineList(redisK, airLineDTOList);
                Map<String, List<AirLineDTO>> airlineListMap = airLineDTOList.stream().collect(Collectors.groupingBy(airlineDto -> (airlineDto.getDepCity() + airlineDto.getArrCity())));
                Map<String, String> redisMap = new HashMap<>();
                airlineListMap.forEach((k, v) -> redisMap.put(k, JsonUtil.objectToJson(v)));
                //默认存放4小时
                redisUtil.putHashAll(redisK, redisMap, 3600 * 4L);
            } catch (Exception e) {
                log.error("航线数据:{},缓存处理异常:", JsonUtil.objectToJson(allResultDTO), e);
            }
        }
        return airLineDTOList;
    }

    @Override
    public BaseResultDTO addBatchAirLines(BaseRequestDTO<List<AirLineDTO>> requestDTO) {
        BaseResultDTO baseResultDTO = feignAirLineService.addBatchAirLines(requestDTO);
        //添加成功之后清空缓存
        if (null != baseResultDTO && baseResultDTO.getResultCode().equals(WSEnum.SUCCESS.resultCode)) {
            redisUtil.remove(RedisKeyConstants.BASIC_AIRLINE);
            List<String> addonRemarkList = AddonRemarkEnum.getAllCode();
            for (String addonRemark : addonRemarkList) {
                String redisK = RedisKeyConstants.BASIC_AIRLINE_ADDON_REMARK + addonRemark;
                redisUtil.remove(redisK);
            }
        }
        return baseResultDTO;
    }

    @Override
    public BaseResultDTO<List<CooperationAirlineDTO>> listCooperationAirline(BaseRequestDTO<CooperationAirlineReqDTO> requestDTO) {
        return cooperationAirlineService.searchAllCooperationAirline(requestDTO);
    }

    @Override
    public BaseResultDTO<List<FlightInfoDTO>> searchflightInfoList(BaseRequestDTO<FlightInfoReqDTO> requestDTO) {
        return feignFlightInfoService.searchflightInfoList(requestDTO);
    }

    @Override
    public BaseResultDTO<List<FlightInfoDTO>> searchThemeFlightInfoList(BaseRequestDTO<FlightInfoReqDTO> requestDTO) {
        FlightInfoReqDTO flightInfoReqDTO = requestDTO.getRequest();
        Objects.requireNonNull(flightInfoReqDTO.getFlightDate(), "flightDate");
        Objects.requireNonNull(flightInfoReqDTO.getDepCity(), "depCity");
        Objects.requireNonNull(flightInfoReqDTO.getArrCity(), "arrCity");
        String redisKey = THEME+":" + flightInfoReqDTO.getFlightDate() + ":" + flightInfoReqDTO.getDepCity() + flightInfoReqDTO.getArrCity();
        String redisExistKey = "EXIST"+THEME+":" + flightInfoReqDTO.getFlightDate() + ":" + flightInfoReqDTO.getDepCity() + flightInfoReqDTO.getArrCity();
        String exist = redisUtil.get(redisExistKey);
        if ("N".equals(exist)) {
            return BaseResultDTO.newSuccess(new ArrayList<>());
        }
        List<String> flightInfoStrList = redisUtil.getHashValues(redisKey);
        if (CollectionUtils.isNotEmpty(flightInfoStrList)) {
            List<FlightInfoDTO> flightInfoDTOList = new ArrayList<>();
            for (String str : flightInfoStrList) {
                flightInfoDTOList.add(JsonUtil.fromJson(str, FlightInfoDTO.class));
            }
            return BaseResultDTO.newSuccess(flightInfoDTOList);
        } else {
            //查询普通的航班计划
            BaseResultDTO<List<FlightInfoDTO>> baseResultDTO = feignFlightInfoService.searchflightInfoList(requestDTO);
            if (!WSEnum.SUCCESS.resultCode.equals(baseResultDTO.getResultCode()) || CollectionUtils.isEmpty(baseResultDTO.getResult())) {
                return BaseResultDTO.newSuccess(new ArrayList<>());
            }
            //查询主题航班信息
            GenshinFlightInfoPo genshinFlightInfoPoParam = new GenshinFlightInfoPo();
            genshinFlightInfoPoParam.setFlightDate(flightInfoReqDTO.getFlightDate());
            genshinFlightInfoPoParam.setDepcity3code(flightInfoReqDTO.getDepCity());
            genshinFlightInfoPoParam.setTheme(THEME);
            List<GenshinFlightInfoPo> genshinFlightInfoPoList = genshinFlightService.selectGenshinFlightInfoList(genshinFlightInfoPoParam);
            if (CollectionUtils.isEmpty(genshinFlightInfoPoList)) {
                return BaseResultDTO.newSuccess(new ArrayList<>());
            }
            for (FlightInfoDTO flightInfoDTO : baseResultDTO.getResult()) {
                boolean hasTheme = genshinFlightInfoPoList.stream()
                        .anyMatch(genshinFlightInfoPo -> flightInfoDTO.getFlightDate().equals(genshinFlightInfoPo.getFlightDate()) && flightInfoDTO.getFlightNo().equals(genshinFlightInfoPo.getFlightNo()) && flightInfoDTO.getDepCity().equals(genshinFlightInfoPo.getDepcity3code()));
                if (hasTheme) {
                    flightInfoDTO.setTheme(THEME);
                }
            }
            List<FlightInfoDTO> flightInfoDTOList = baseResultDTO.getResult().stream().filter(flightInfoDTO -> THEME.equals(flightInfoDTO.getTheme())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(flightInfoDTOList)) {
                Map<String, String> flightInfoDTOMap = flightInfoDTOList.stream().collect(Collectors.toMap(FlightInfoDTO::getFlightKey, obj -> JsonUtil.objectToJson(obj)));
                redisUtil.putHashAll(redisKey, flightInfoDTOMap, 3600);
                redisUtil.set(redisExistKey, "Y", 3600L);
            } else {
                redisUtil.set(redisExistKey, "N", 3600L);
            }
            //只保留主题航
            return BaseResultDTO.newSuccess(flightInfoDTOList);
        }
    }

    @Override
    public Map<String, Object> queryCommonFlyCityDetailsByCityCode(CommonFlyCityReqDTO requestDTO) {
        return feignCommonflyCityService.queryCommonFlyCityDetailsByCityCode(requestDTO);
    }

    @Override
    public BaseResultDTO<List<TrrFlightInfoDTO>> searchTrrFlightinfo(BaseRequestDTO<ParamTrrFlightInfo> requestDTO) {
        return feignFlightInfoService.searchTrrFlightInfo(requestDTO);
    }

    @Override
    public BaseResultDTO<List<FlightTicketPriceByYResp>> searchFlightTicketPriceByYAndCity(BaseRequestDTO<List<FlightTicketPriceByYDTO>> requestDTO) {
        return feignFlightInfoService.searchFlightTicketPriceByYAndCity(requestDTO);
    }

    @Override
    public List<ThemeAirline> searchFlightByDate(ThemeFlightParam themeFlightParam) {
        LocalDate tripDate = DateUtils.getLocalDate(themeFlightParam.getFlightDate(), DateUtils.STRING_FORMAT_YYYY_MM_DD);
        LocalDate startDate = DateUtils.getLocalDate(flightConfig.getStartFlightDate(), DateUtils.STRING_FORMAT_YYYY_MM_DD);
        String queryDate = themeFlightParam.getFlightDate();
        if (tripDate.isBefore(startDate)) {
            queryDate = flightConfig.getStartFlightDate();
        }
        GenshinFlightInfoPo genshinFlightInfoPo = new GenshinFlightInfoPo();
        genshinFlightInfoPo.setTheme(THEME);
        genshinFlightInfoPo.setFlightDate(queryDate);
        List<GenshinFlightInfoPo> genshinFlightInfoPoList = genshinFlightService.selectGenshinFlightInfoList(genshinFlightInfoPo);
        if (CollectionUtils.isEmpty(genshinFlightInfoPoList)) {
            return new ArrayList<>();
        }
        List<FlightInfoDTO> flightInfoDTOList = new ArrayList<>();
        BaseRequestDTO<FlightInfoReqDTO> requestDTO = createBaseRequestDTO();
        FlightInfoReqDTO flightInfoReqDTO = new FlightInfoReqDTO();
        for (GenshinFlightInfoPo param : genshinFlightInfoPoList) {
            flightInfoReqDTO.setFlightDate(queryDate);
            flightInfoReqDTO.setFlightNo(param.getFlightNo());
            flightInfoReqDTO.setDepAirport(param.getDepAirportCode());
            requestDTO.setRequest(flightInfoReqDTO);
            BaseResultDTO<List<FlightInfoDTO>> baseResultDTO = feignFlightInfoService.searchflightInfoList(requestDTO);
            if (CollectionUtils.isNotEmpty(baseResultDTO.getResult())) {
                flightInfoDTOList.addAll(baseResultDTO.getResult());
            }
        }
        List<ThemeAirline> themeAirlineList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(flightInfoDTOList)) {
            //按照出发时刻排序
            flightInfoDTOList.sort(Comparator.comparing(FlightInfoDTO::getDepDateChinaTime));
            for (FlightInfoDTO flightInfoDTO : flightInfoDTOList) {
                ThemeAirline themeAirline = new ThemeAirline();
                themeAirline.setFlightDate(flightInfoDTO.getFlightDate());
                themeAirline.setFlightNo(flightInfoDTO.getFlightNo());
                themeAirline.setDepCity(flightInfoDTO.getDepCity());
                themeAirline.setDepAirport(flightInfoDTO.getDepAirport());
                themeAirline.setDepTerminal(flightInfoDTO.getDepAirportTerminal());
                themeAirline.setStd(flightInfoDTO.getDepDateTime());
                themeAirline.setArrCity(flightInfoDTO.getArrCity());
                themeAirline.setArrAirport(flightInfoDTO.getArrAirport());
                themeAirline.setArrTerminal(flightInfoDTO.getArrAirportTerminal());
                themeAirline.setSta(flightInfoDTO.getArrDateTime());
                themeAirlineList.add(themeAirline);
            }
        }
        return themeAirlineList;
    }

    @Override
    public List<String> searchThemeCalendar() {
        LocalDate nowDate = LocalDate.now();
        LocalDate startDate = DateUtils.getLocalDate(flightConfig.getStartFlightDate(), DateUtils.STRING_FORMAT_YYYY_MM_DD);
        String queryDate = DateUtils.getDateString(nowDate, DateUtils.STRING_FORMAT_YYYY_MM_DD);
        if (nowDate.isBefore(startDate)) {
            queryDate = flightConfig.getStartFlightDate();
        }
        return genshinFlightService.selectThemeCalendar(queryDate);
    }

}
