package com.juneyaoair.flightbasic.hystric;


import com.juneyaoair.flightbasic.request.wechat.AppPictureDTO;

import com.juneyaoair.flightbasic.feign.api.FeignAppPictureService;
import com.juneyaoair.flightbasic.response.icon.AppPicturePartDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: flightbasic
 * @description
 * @author: z<PERSON><PERSON><PERSON>
 * @create: 2019-04-22 13:36
 **/
@Component
@Slf4j
public class AppPictureServiceHystric implements FeignAppPictureService {
    @Override
    public List<AppPicturePartDTO> getValidList(AppPictureDTO appPictureDTO) {
        log.debug("信息查询出错！！");
        return null;
    }
}
