package com.juneyaoair.flightbasic.feign.basic;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.hystric.FlightInfoHystrix;
import com.juneyaoair.flightbasic.request.flightInfo.FlightInfoReqDTO;
import com.juneyaoair.flightbasic.request.flightTicketPrice.FlightTicketPriceByYDTO;
import com.juneyaoair.flightbasic.request.trrFlightInfo.ParamTrrFlightInfo;
import com.juneyaoair.flightbasic.response.flightInfo.FlightInfoDTO;
import com.juneyaoair.flightbasic.response.flightPrice.FlightTicketPriceByYResp;
import com.juneyaoair.flightbasic.response.trrFlightInfo.TrrFlightInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "flightbasic-provider-service", fallback = FlightInfoHystrix.class, contextId = "FeignFlightInfoService")
public interface FeignFlightInfoService {
    @PostMapping("basic/flightInfoList")
    BaseResultDTO<List<FlightInfoDTO>> searchflightInfoList(@RequestBody BaseRequestDTO<FlightInfoReqDTO> requestDTO);
    @PostMapping("basic/searchTrrFlightInfo")
    BaseResultDTO<List<TrrFlightInfoDTO>> searchTrrFlightInfo(BaseRequestDTO<ParamTrrFlightInfo> requestDTO);
    @PostMapping("basic/searchFlightTicketPriceByYAndCity")
    BaseResultDTO<List<FlightTicketPriceByYResp>> searchFlightTicketPriceByYAndCity(@RequestBody BaseRequestDTO<List<FlightTicketPriceByYDTO>> requestDTO);
}
