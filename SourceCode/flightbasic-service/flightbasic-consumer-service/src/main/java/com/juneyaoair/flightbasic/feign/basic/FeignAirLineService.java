package com.juneyaoair.flightbasic.feign.basic;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.hystric.AirLineHystrix;
import com.juneyaoair.flightbasic.request.airLine.AirLineReqDTO;
import com.juneyaoair.flightbasic.response.airline.AirLineDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@FeignClient(value = "flightbasic-provider-service",fallback = AirLineHystrix.class, contextId = "FeignAirLineService")
public interface FeignAirLineService {
    @RequestMapping("/basic/airLines")
    public BaseResultDTO<List<AirLineDTO>> getAllAirLine(@RequestBody BaseRequestDTO<AirLineReqDTO> requestDTO);
    @PostMapping("/basic/airLines/addBatch")
    public BaseResultDTO addBatchAirLines(@RequestBody BaseRequestDTO<List<AirLineDTO>> requestDTO);
}
