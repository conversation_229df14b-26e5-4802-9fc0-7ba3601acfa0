package com.juneyaoair.flightbasic.hystric;

import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.feign.customs.CustomsService;
import com.juneyaoair.flightbasic.utils.MdcUtils;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: caolei
 * @Description: 海关路由服务
 * @Date: 2022/11/3 13:47
 * @Modified by:
 */
@Slf4j
@Component
public class CustomsHystrix implements CustomsService {

    @Override
    public BaseResultDTO<String> sendToShaCustoms(Request.Options options) {
        log.error("推送数据给上海网关调用provider服务出现异常，请求ID：{} ", MdcUtils.getRequestId());
        return BaseResultDTO.failResult(WSEnum.SERVICE_FUSING.resultCode, WSEnum.SERVICE_FUSING.resultInfo);
    }
}