package com.juneyaoair.flightbasic.service.wifi;

import com.juneyaoair.flightbasic.common.BaseReqDTO;
import com.juneyaoair.flightbasic.common.BaseRespDTO;
import com.juneyaoair.flightbasic.request.wifi.*;

public interface OnboardWIFIService {
    BaseRespDTO orderWIFI(BaseReqDTO<OrderWiFiReqDTO> requestDTO);

    BaseRespDTO getWifiStock(BaseReqDTO<WifiStockReqDTO> requestDTO);

    BaseRespDTO getOrderRecord(BaseReqDTO<WIFIRecordReqDTO> requestDTO);

    BaseRespDTO WiFiCancelBooking(BaseReqDTO<WiFiCancelBookingReqDTO> requestDTO);

    BaseRespDTO updateWifiStock(BaseReqDTO<WIFIStockUpdateReqDTO> requestDTO);
}
