package com.juneyaoair.flightbasic.service.sceneqrcode.Impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.cuss.dto.booking.request.checkin.CheckInInfoQueryByMemberIdRequestDTO;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.PageDomain;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.exception.CommonException;
import com.juneyaoair.flightbasic.external.bean.bigdata.HighValueData;
import com.juneyaoair.flightbasic.external.service.BigDataOdsService;
import com.juneyaoair.flightbasic.mapper.activity.sceneqrcode.MpSceneQrCodeRecordMapper;
import com.juneyaoair.flightbasic.mapper.activity.sceneqrcode.MpSceneQrcodeLogInfoMapper;
import com.juneyaoair.flightbasic.mapper.activity.sceneqrcode.MpSceneQrcodeStatisticalMapper;
import com.juneyaoair.flightbasic.model.activity.sceneqrcode.MpSceneQrCodeRecordPO;
import com.juneyaoair.flightbasic.model.activity.sceneqrcode.MpSceneQrcodeLoginfo;
import com.juneyaoair.flightbasic.model.activity.sceneqrcode.MpSceneQrcodeStatistical;
import com.juneyaoair.flightbasic.response.activity.ActivityInnerPagesDTO;
import com.juneyaoair.flightbasic.sceneqrcode.QueryCheckInTicketResponse;
import com.juneyaoair.flightbasic.service.sceneqrcode.MpSceneQrcodeRecordService;
import com.juneyaoair.flightbasic.utils.IpUtils;
import com.juneyaoair.flightbasic.utils.PageUtils;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since Created at 2022-10-25 10:07
 */
@Service
@Slf4j
@Transactional
public class MpSceneQrcodeRecordServiceImpl implements MpSceneQrcodeRecordService {


    @Resource
    private MpSceneQrCodeRecordMapper mpSceneQrCodeRecordMapper;

    @Resource
    private BigDataOdsService bigDataOdsService;

    @Resource
    private MpSceneQrcodeStatisticalMapper mpSceneQrcodeStatisticalMapper;

    @Resource
    private MpSceneQrcodeLogInfoMapper mpSceneQrcodeLogInfoMapper;

    @Value("${checkin_select_url}")
    private String CHECKIN_SELECT_URL;


    @Override
    public void statisticalIntegral(String jobParam) {
        try {
            Integer num = 2;
            if (!StringUtil.isNullOrEmpty(jobParam)) {
                num = Integer.valueOf(jobParam);
            }
            int pageSize = 500; // 每页大小
            int pageNum = 0; // 页码

            List<MpSceneQrCodeRecordPO>   mpSceneQrcodeRecords=  getDataFromDatabase(pageNum,pageSize,num);

            while (!mpSceneQrcodeRecords.isEmpty()) {
                // 处理数据
                processBatchNetworkRequest(mpSceneQrcodeRecords);

                pageNum++; // 下一页
                mpSceneQrcodeRecords = getDataFromDatabase(pageNum, pageSize,num);
            }

        } catch (Exception e) {
            StackTraceElement stackTraceElement = e.getStackTrace()[0];
            log.info("出错行数 {}，内容{}", stackTraceElement.getLineNumber(), e.getMessage());
            throw new CommonException(WSEnum.ERROR.resultCode, stackTraceElement.getFileName() + "出错行数 " +
                    stackTraceElement.getLineNumber() + "，内容:" + e.getMessage());
        }


    }



    private  List<MpSceneQrCodeRecordPO>  getDataFromDatabase(Integer pageNum,Integer pageSize,Integer num){
        String   date   =DateUtils.getDateString(DateUtils.addOrLessDay(new Date(),-num));
        String   startDate = date+" 00:00:00";
        String   endDate   = date+" 23:59:59";
        PageHelper.startPage(pageNum, pageSize,true);
        List<MpSceneQrCodeRecordPO> mpSceneQrcodeRecords  = mpSceneQrCodeRecordMapper.queryAllByTime(startDate,endDate);
        //获取未统计记录的数据
      return  mpSceneQrcodeRecords;
    }


    /**
     * 批处理网络请求
     * @param mpSceneQrcodeRecords
     */
    public void processBatchNetworkRequest(List<MpSceneQrCodeRecordPO> mpSceneQrcodeRecords){
        Map<String, Boolean> map = null;
        if (CollectionUtils.isNotEmpty(mpSceneQrcodeRecords)) {
            //根据值机记录判断   获客场景
            mpSceneQrcodeRecords.forEach(mpSceneQrcodeRecord -> getGuestScenario(mpSceneQrcodeRecord));
            List<String> ffpCardNoList = mpSceneQrcodeRecords.stream().map(MpSceneQrCodeRecordPO::getFfpCardNo).collect(Collectors.toList());
            List<HighValueData> highValueDataList = getCustomerType(ffpCardNoList);

            if (CollectionUtils.isNotEmpty(highValueDataList)) {
                map = highValueDataList.stream().collect(Collectors.toMap(HighValueData::getMemberId, e -> "Y".equals(e.getHighValue())));
            } else {
                map = new HashMap<>();
            }
        }
        processBatchData(mpSceneQrcodeRecords,map);
    }



    /**
     * 批处理数据库数据
     * @param mpSceneQrcodeRecords
     */
    @Transactional
    public void  processBatchData( List<MpSceneQrCodeRecordPO> mpSceneQrcodeRecords,Map<String, Boolean> map){
        if (CollectionUtils.isNotEmpty(mpSceneQrcodeRecords)&&map!=null&&map.size()>0) {
            Map<String, Boolean> finalMap = map;
            mpSceneQrcodeRecords.forEach(mpSceneQrcodeRecord -> {
                        MpSceneQrcodeStatistical mpSceneQrcodeStatistical = mpSceneQrcodeStatisticalMapper.selectByName(mpSceneQrcodeRecord.getName(), mpSceneQrcodeRecord.getSceneSource());
                        Boolean insert = false;
                        MpSceneQrcodeLoginfo mpSceneQrcodeLoginfo = new MpSceneQrcodeLoginfo();
                        mpSceneQrcodeLoginfo.setCreateDate(DateUtils.getDateStringAllDate(new Date()));
                        if (ObjectUtil.isEmpty(mpSceneQrcodeStatistical)) {
                            mpSceneQrcodeStatistical = new MpSceneQrcodeStatistical();
                            mpSceneQrcodeStatistical.setId(IdUtil.fastSimpleUUID());
                            mpSceneQrcodeStatistical.setSceneSource(mpSceneQrcodeRecord.getSceneSource());
                            mpSceneQrcodeStatistical.setName(mpSceneQrcodeRecord.getName());
                            insert = true;
                        }
                        initStatistical(mpSceneQrcodeStatistical);
                        if (finalMap.get(mpSceneQrcodeRecord.getFfpCardNo()) == null) {
                            log.info("会员卡号 : {},大数据返回是否为高价旅客为空", mpSceneQrcodeRecord.getFfpCardNo());
                            mpSceneQrcodeRecord.setGuestIntegral("-");
                            mpSceneQrcodeRecord.setCustomerType("-");
                            mpSceneQrCodeRecordMapper.update(mpSceneQrcodeRecord);
                        } else {
                            mpSceneQrcodeRecord.setCustomerType(finalMap.get(mpSceneQrcodeRecord.getFfpCardNo()) ? "1" : "2");
                            if ("1".equals(mpSceneQrcodeRecord.getCustomerType()) && "1".equals(mpSceneQrcodeRecord.getGuestScenario())) {
                                mpSceneQrcodeRecord.setGuestIntegral("50");
                                mpSceneQrcodeStatistical.setHighAirportCount(1);
                                mpSceneQrcodeStatistical.setPointQuantity(50);
                                mpSceneQrcodeLoginfo.setLogInfo("日期:" + mpSceneQrcodeLoginfo.getCreateDate() + ",会员卡号为:" + mpSceneQrcodeRecord.getFfpCardNo() + "是机场高价值旅客，获得绩点--50");
                            }
                            if ("2".equals(mpSceneQrcodeRecord.getCustomerType()) && "1".equals(mpSceneQrcodeRecord.getGuestScenario())) {
                                mpSceneQrcodeRecord.setGuestIntegral("10");
                                mpSceneQrcodeStatistical.setOrdAirportCount(1);
                                mpSceneQrcodeStatistical.setPointQuantity(10);
                                mpSceneQrcodeLoginfo.setLogInfo("日期:" + mpSceneQrcodeLoginfo.getCreateDate() + ",会员卡号为:" + mpSceneQrcodeRecord.getFfpCardNo() + "是机场一般旅客，获得绩点--10");
                            }
                            if ("1".equals(mpSceneQrcodeRecord.getCustomerType()) && "2".equals(mpSceneQrcodeRecord.getGuestScenario())) {
                                mpSceneQrcodeRecord.setGuestIntegral("50");
                                mpSceneQrcodeStatistical.setHighDifIndustryCount(1);
                                mpSceneQrcodeStatistical.setPointQuantity(50);
                                mpSceneQrcodeLoginfo.setLogInfo("日期:" + mpSceneQrcodeLoginfo.getCreateDate() + ",会员卡号为:" + mpSceneQrcodeRecord.getFfpCardNo() + "是异业高价值旅客，获得绩点--50");
                            }
                            if ("2".equals(mpSceneQrcodeRecord.getCustomerType()) && "2".equals(mpSceneQrcodeRecord.getGuestScenario())) {
                                mpSceneQrcodeRecord.setGuestIntegral("5");
                                mpSceneQrcodeStatistical.setOrdDifIndustryCount(1);
                                mpSceneQrcodeStatistical.setPointQuantity(5);
                                mpSceneQrcodeLoginfo.setLogInfo("日期:" + mpSceneQrcodeLoginfo.getCreateDate() + ",会员卡号为:" + mpSceneQrcodeRecord.getFfpCardNo() + "是异业一般旅客，获得绩点--5");
                            }
                            if (insert) {
                                mpSceneQrcodeStatisticalMapper.insert(mpSceneQrcodeStatistical);
                            } else {
                                mpSceneQrcodeStatisticalMapper.updateIntegral(mpSceneQrcodeStatistical);
                            }
                            mpSceneQrCodeRecordMapper.update(mpSceneQrcodeRecord);

                            mpSceneQrcodeLoginfo.setId(StringUtil.newGUID());

                            mpSceneQrcodeLoginfo.setSqsId(mpSceneQrcodeStatistical.getId());
                            mpSceneQrcodeLoginfo.setCreateUser("flightBasicJob");
                            mpSceneQrcodeLogInfoMapper.insert(mpSceneQrcodeLoginfo);
                        }
                    }
            );


        }
    }



    private void initStatistical(MpSceneQrcodeStatistical mpSceneQrcodeStatistical) {
        mpSceneQrcodeStatistical.setPointQuantity(0);
        mpSceneQrcodeStatistical.setHighDifIndustryCount(0);
        mpSceneQrcodeStatistical.setHighAirportCount(0);
        mpSceneQrcodeStatistical.setOrdAirportCount(0);
        mpSceneQrcodeStatistical.setOrdDifIndustryCount(0);
    }

    /**
     * 判断是否是高价值旅客
     *
     * @param
     */
    public List<HighValueData> getCustomerType(List<String> ffpCardNoList) {
        return bigDataOdsService.getHighValuePassenger(ffpCardNoList);
    }


    /**
     * 根据值机记录判断   获客场景  1机场  2异业
     *
     * @param mpSceneQrcodeRecord
     * @return
     */
    public void getGuestScenario(MpSceneQrCodeRecordPO mpSceneQrcodeRecord) {
                  String  dateString =StringUtil.isNullOrEmpty(mpSceneQrcodeRecord.getAssociatedTime())?DateUtils.getCurrentDateTimesStr():mpSceneQrcodeRecord.getAssociatedTime();
                  Date  date = DateUtils.toDate(dateString,DateUtils.YYYY_MM_DD_HH_MM_SS);
                  date= date==null?new Date():date;
                  String beginDate= DateUtils.getDateString( DateUtils.addOrLessDay(date,-1)) ;
                  String endDate = DateUtils.getDateString( DateUtils.addOrLessDay(date,1));
                Boolean rs = bigDataOdsService.flightRecord(beginDate,endDate,mpSceneQrcodeRecord.getFfpCardNo());
                if (rs){
                    //机场获客
                    mpSceneQrcodeRecord.setGuestScenario("1");
                }else {
                    //异业获客
                    mpSceneQrcodeRecord.setGuestScenario("2");
                }
    }


}
