package com.juneyaoair.flightbasic.service.impl;

import com.juneyaoair.flightbasic.service.IGenshinImpactFlightBizService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/8 14:21
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Profile("dev")
@Slf4j
public class GenshinImpactFlightBizServiceTest {
    @Autowired
    private IGenshinImpactFlightBizService genshinImpactFlightBizService;

    @Test
    public void initGenshinImpactFlight() {
        genshinImpactFlightBizService.initGenshinImpactFlight(1,7);
    }
}