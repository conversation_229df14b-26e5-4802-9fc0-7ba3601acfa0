-- Create table 中免匹配 流水表
create table "T_ZHONGMIAN_RECORD"
(
    id            VARCHAR2(50) not null,
    ffp_id        VARCHAR2(20) not null,
    ffp_card_no   VARCHAR2(20) not null,
    member_name   VARCHAR2(50) not null,
    mobile_no     VARCHAR2(20) not null,
    id_number     VARCHAR2(20) not null,
    level_no      VARCHAR2(20) not null,
    status        VARCHAR2(10) not null,
    failed_reason VARCHAR2(200), --如果未失败 则可以不记录失败原因
    create_user   VARCHAR2(200) not null,
    create_time   DATE not null,
    PRIMARY KEY ("ID")
)

-- Add comments to the columns
comment
on column T_ZHONGMIAN_RECORD.ffp_id is '会员ID';
comment
on column T_ZHONGMIAN_RECORD.ffp_card_no is '会员卡号';
comment
on column T_ZHONGMIAN_RECORD.member_name is '会员姓名';
comment
on column T_ZHONGMIAN_RECORD.mobile_no is '会员手机号';
comment
on column T_ZHONGMIAN_RECORD.id_number is '身份证号';
comment
on column T_ZHONGMIAN_RECORD.level_no is '等级编码（中免侧）';
comment
on column T_ZHONGMIAN_RECORD.status is '匹配状态';
comment
on column T_ZHONGMIAN_RECORD.failed_reason is '失败原因（如有则记录）'

CREATE UNIQUE INDEX "ix_cr_fcn_st" ON "T_ZHONGMIAN_RECORD" ("FFP_CARD_NO", "STATUS")