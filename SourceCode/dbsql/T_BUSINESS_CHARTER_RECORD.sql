-- Create table
create table T_BUSINESS_CHARTER_RECORD
(
  id               VARCHAR2(100) not null,
  ffp_card_no      VARCHAR2(10) not null,
  dept_city_name           VARCHAR2(50),
  arr_city_name         VARCHAR2(50),
  depart_date      VARCHAR2(10),
  client_name      VARCHAR2(20),
  mobile          VARCHAR2(20),
  apply_date       VARCHAR2(20)
)

-- Add comments to the columns
comment on column T_BUSINESS_CHARTER_RECORD.ffp_card_no
  is '会员卡号';
comment on column T_BUSINESS_CHARTER_RECORD.dept_city_name
  is '出发城市名';
comment on column T_BUSINESS_CHARTER_RECORD.arr_city_name
  is '到达城市名';
comment on column T_BUSINESS_CHARTER_RECORD.depart_date
  is '出发日期';
comment on column T_BUSINESS_CHARTER_RECORD.client_name
  is '客户姓名';
comment on column T_BUSINESS_CHARTER_RECORD.mobile
  is '手机号';
comment on column T_BUSINESS_CHARTER_RECORD.apply_date
  is '申请日期';


CREATE UNIQUE INDEX "T_B2C2013"."CHARTER_RECORD_ID_IDX" ON "T_B2C2013"."T_BUSINESS_CHARTER_RECORD" ("ID")

CREATE INDEX "T_B2C2013"."CHARTER_RECORD_CARD_NO_IDX" ON "T_B2C2013"."T_BUSINESS_CHARTER_RECORD" ("FFP_CARD_NO", "APPLY_DATE") 

