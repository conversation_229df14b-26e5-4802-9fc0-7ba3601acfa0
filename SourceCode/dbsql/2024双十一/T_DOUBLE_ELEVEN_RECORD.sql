-- Create table 飞行礼遇 · 贵宾体验 参与结果流水表
create table "T_DOUBLE_ELEVEN_RECORD"
(
    id               VARCHAR2(100) not null,
    ffp_id           VARCHAR2(20) not null,
    ffp_card_no      VARCHAR2(20) not null,
    participate_type VARCHAR2(50) not null,
    participate_date VARCHAR2(50) not null,
    prize_code VARCHAR2(50) not null,
    prize_name VARCHAR2(50) not null,
    status           VARCHAR2(10) not null,
    failed_reason    CLOB,       --如未遇失败情况，则无需记录失败原因
    create_time      DATE not null,
    create_user      VARCHAR2(50) not null,
    update_time      DATE,--如未遇更新，则无更新时间。故可为空
    update_user      VARCHAR2(50)--如未遇更新，则无更新人。故可为空
)

-- Add comments to the columns
    comment on column T_DOUBLE_ELEVEN_RECORD.ffp_id is '会员ID';
comment on column T_DOUBLE_ELEVEN_RECORD.ffp_card_no is '会员卡号';
comment on column T_DOUBLE_ELEVEN_RECORD.participate_type is '参与抽奖的类型';
comment on column T_DOUBLE_ELEVEN_RECORD.participate_date is '参与抽奖的日期';
comment on column T_DOUBLE_ELEVEN_RECORD.prize_code is '奖品编码';
comment on column T_DOUBLE_ELEVEN_RECORD.prize_name is '奖品名称';
comment on column T_DOUBLE_ELEVEN_RECORD.status is '奖品发送状态 T-成功 F-失败 U-状态未知';
comment on column T_DOUBLE_ELEVEN_RECORD.failed_reason is '会员级别调整失败原因（如有则记录）';
comment on column T_DOUBLE_ELEVEN_RECORD.create_time is '创建时间';
comment on column T_DOUBLE_ELEVEN_RECORD.create_user is '创建人';
comment on column T_DOUBLE_ELEVEN_RECORD.update_time is '更新时间';
comment on column T_DOUBLE_ELEVEN_RECORD.update_user is '更新人';

CREATE UNIQUE INDEX "T_B2C2013"."ELEVEN_ID" ON "T_B2C2013"."T_DOUBLE_ELEVEN_RECORD" ("ID")
CREATE UNIQUE INDEX "T_B2C2013"."ELEVEN_FPP" ON "T_B2C2013"."T_DOUBLE_ELEVEN_RECORD" ("FFP_CARD_NO", "PARTICIPATE_TYPE", "PARTICIPATE_DATE")

ALTER TABLE T_DOUBLE_ELEVEN_RECORD
ADD CONSTRAINT uk_card_day_type UNIQUE (FFP_CARD_NO, PARTICIPATE_TYPE,PARTICIPATE_DATE);

