-- Create table 积分找回奖品发放流水表
create table "T_POINT_PRIZE_RECEIVE_RECORD"
(
    id             VARCHAR2(100) not null,
    ffp_card_no    VARCHAR2(20) not null,
    prize_code     VARCHAR2(50) not null,
    prize_id       VARCHAR2(50) not null,
    prize_type     VARCHAR2(20) not null,
    prize_amount   NUMBER(8,0) not null,
    flight_date    VARCHAR2(20) not null,
    receive_time   VARCHAR2(20) not null,
    receive_status VARCHAR2(10) not null,
    create_time    DATE not null,
    create_user    VARCHAR2(50) not null,
    update_time    DATE,--如未遇更新，则无更新时间。故可为空
    update_user    VARCHAR2(50),--如未遇更新，则无更新人。故可为空
    PRIMARY KEY ("ID")
)
-- Add comment to the table
    comment on table T_POINT_PRIZE_RECEIVE_RECORD is '积分找回奖品领取表';
-- Add comments to the columns
comment
on column T_POINT_PRIZE_RECEIVE_RECORD.ffp_card_no is '会员卡号';
comment
on column T_POINT_PRIZE_RECEIVE_RECORD.prize_code is '奖品编码';
comment
on column T_POINT_PRIZE_RECEIVE_RECORD.prize_id is '奖品ID 用于实际的奖品发放';
comment
on column T_POINT_PRIZE_RECEIVE_RECORD.prize_type is '奖品类型';
comment
on column T_POINT_PRIZE_RECEIVE_RECORD.prize_amount is '奖品发放数量';
comment
on column T_POINT_PRIZE_RECEIVE_RECORD.flight_date is '飞行时间';
comment
on column T_POINT_PRIZE_RECEIVE_RECORD.receive_time is '领取时间';
comment
on column T_POINT_PRIZE_RECEIVE_RECORD.receive_status is '领取状态';
comment
on column T_POINT_PRIZE_RECEIVE_RECORD.create_time is '创建时间';
comment
on column T_POINT_PRIZE_RECEIVE_RECORD.create_user is '创建人';
comment
on column T_POINT_PRIZE_RECEIVE_RECORD.update_time is '更新时间';
comment
on column T_POINT_PRIZE_RECEIVE_RECORD.update_user is '更新人';

CREATE UNIQUE INDEX ux_rpr_fp_at ON T_POINT_PRIZE_RECEIVE_RECORD (FFP_CARD_NO, PRIZE_CODE)

