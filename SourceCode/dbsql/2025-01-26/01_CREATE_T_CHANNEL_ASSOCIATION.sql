CREATE TABLE "T_CHANNEL_ASSOCIATION" (
    "ASSOCIATION_ID" VARCHAR2(64) NOT NULL,
    "ASSOCIATION_TYPE" VARCHAR2(32) NOT NULL,
    "SOURCE_ID" VARCHAR2(64) NOT NULL,
    "CHANNEL_CODE" VARCHAR2(32) NOT NULL,
    "CREATE_TIME" DATE NOT NULL,
    "CREATE_USER" VARCHAR2(32) NOT NULL,
    "UPDATE_TIME" DATE NOT NULL,
    "UPDATE_USER" VARCHAR2(32) NOT NULL,
     PRIMARY KEY ("ASSOCIATION_ID")
);

COMMENT ON TABLE T_CHANNEL_ASSOCIATION  IS '渠道关联表';
COMMENT ON COLUMN T_CHANNEL_ASSOCIATION.ASSOCIATION_ID IS '关联ID';
COMMENT ON COLUMN T_CHANNEL_ASSOCIATION.ASSOCIATION_TYPE IS '关联类型 参照：ChannelAssociationTypeEnum';
COMMENT ON COLUMN T_CHANNEL_ASSOCIATION.SOURCE_ID IS '来源数据ID';
COMMENT ON COLUMN T_CHANNEL_ASSOCIATION.CHANNEL_CODE IS '渠道号';
COMMENT ON COLUMN T_CHANNEL_ASSOCIATION.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN T_CHANNEL_ASSOCIATION.CREATE_USER IS '创建人';
COMMENT ON COLUMN T_CHANNEL_ASSOCIATION.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN T_CHANNEL_ASSOCIATION.UPDATE_USER IS '更新人';

CREATE UNIQUE INDEX UX_CA_TYPE_ID_CHANNEL ON T_CHANNEL_ASSOCIATION(ASSOCIATION_TYPE,SOURCE_ID,CHANNEL_CODE);
