-- Create table
create table T_ACTIVITY_STAFF_INVITE_RECORD
(
  id           VARCHAR2(50) not null,
  ffp_card_no  VARCHAR2(50),
  ffp_id       VARCHAR2(50),
  encrypted_id VARCHAR2(50),
  share_url    VARCHAR2(100),
  status       VARCHAR2(10),
  ffp_mobile   VARCHAR2(50)
)
tablespace HOORDER
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
-- Create/Recreate indexes
create index IX_STAFF_CARD_NO on T_ACTIVITY_STAFF_INVITE_RECORD (FFP_CARD_NO)
  tablespace HOORDER
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
create index IX_STAFF_FFP_ID on T_ACTIVITY_STAFF_INVITE_RECORD (FFP_ID)
  tablespace HOORDER
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
create unique index UN_STAFF_RECORD on T_ACTIVITY_STAFF_INVITE_RECORD (FFP_CARD_NO, FFP_ID)
  tablespace HOORDER
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
-- Create/Recreate primary, unique and foreign key constraints
alter table T_ACTIVITY_STAFF_INVITE_RECORD
  add constraint PK_STAFF_INVITE_ID primary key (ID)
  using index
  tablespace HOORDER
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
