package com.juneyaoair.flightbasic;

import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.exception.CommonException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 错误路径处理方法
 * 当系统发生错误时，该方法会捕获错误并根据HTTP状态码抛出相应的业务异常
 *
 * @param request HTTP请求对象，用于获取原始请求URL等信息
 * @param response HTTP响应对象，用于获取错误状态码
 * @throws CommonException 根据不同的HTTP状态码抛出对应的业务异常
 */
/*@Slf4j
@Controller
public class ErrorPageController {
    @RequestMapping("/error")
    public void errorPathHandler(HttpServletRequest request, HttpServletResponse response) {
        // 获取原始请求的URL
        String originalURL = request.getRequestURL().toString();
        log.error("Error occurred for URL: {}", originalURL);
        //抛出ErrorPageException异常，方便被ExceptionHandlerConfig处理
        WSEnum errorEnum;
        switch (response.getStatus()) {
            case 404:
                errorEnum = WSEnum.NOT_FOUND;
                break;
            case 403:
                errorEnum = WSEnum.FORBIDDEN;
                break;
            case 401:
                errorEnum = WSEnum.UNAUTHORIZED;
                break;
            case 400:
                errorEnum = WSEnum.BAD_REQUEST;
                break;
            case 500:
                errorEnum = WSEnum.INTERNAL_SERVER_ERROR;
                break;
            default:
                errorEnum = WSEnum.UNKNOWN;
                break;
        }
        throw new CommonException(errorEnum.resultCode,errorEnum.resultInfo);
    }
}*/
