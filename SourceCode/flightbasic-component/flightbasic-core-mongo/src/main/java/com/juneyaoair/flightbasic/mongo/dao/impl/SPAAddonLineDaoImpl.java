package com.juneyaoair.flightbasic.mongo.dao.impl;

import com.juneyaoair.flightbasic.flight.ConnectFlightCombInfoD;
import com.juneyaoair.flightbasic.mongo.dao.SPAAddonLineDao;
import com.juneyaoair.flightbasic.mongo.po.SPAAddonLine;
import com.mongodb.BasicDBObject;
import com.mongodb.client.MongoCollection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/14 16:31
 */
@Slf4j
@Repository
public class SPAAddonLineDaoImpl implements SPAAddonLineDao {
    @Resource
    private MongoTemplate mongoTemplate;

    private final String collectionName = "SPAAddonLine";

    @Override
    public List<SPAAddonLine> query(String depCity, String arrCity, String flightDate) {
        // 参数校验
        if (depCity == null || depCity.isEmpty()) {
            throw new IllegalArgumentException("depCity cannot be null or empty");
        }
        if (arrCity == null || arrCity.isEmpty()) {
            throw new IllegalArgumentException("arrCity cannot be null or empty");
        }
        if (flightDate == null || flightDate.isEmpty()) {
            throw new IllegalArgumentException("flightDate cannot be null or empty");
        }
        Query query = new Query();
        Criteria criteria = Criteria.where("flightDate").is(flightDate)
                .and("depCity").is(depCity)
                .and("arrCity").is(arrCity);
        query.addCriteria(criteria);
        return mongoTemplate.find(query, SPAAddonLine.class, collectionName);
    }

    @Override
    public void addDocBatch(List<SPAAddonLine> spaAddonLineList) {
        List<BasicDBObject> documents = new ArrayList<>();
        Date date = new Date();
        for (SPAAddonLine line : spaAddonLineList) {
            BasicDBObject object = new BasicDBObject();
            object.put("flightDate", line.getFlightDate());
            object.put("depAirport", line.getDepAirport());
            object.put("arrAirport", line.getArrAirport());
            object.put("stopAirport", line.getStopAirport());
            object.put("depCity", line.getDepCity());
            object.put("arrCity", line.getArrCity());
            object.put("stopCity", line.getStopCity());
            object.put("combFlightNo", line.getCombFlightNo());
            object.put("connectFlightInfos", line.getConnectFlightInfos());
            object.put("source", line.getSource());
            object.put("type", line.getType());
            object.put("createTime", date);
            documents.add(object);
        }
        List<BasicDBObject> list = (List<BasicDBObject>) mongoTemplate.insert(documents, collectionName);
        log.info("此次新增数据{}条", list.size());
    }

    @Override
    public void removeDoc(List<SPAAddonLine> spaAddonLineList, String flightDate, String type) {
        BasicDBObject removeObject = new BasicDBObject();
        MongoCollection collection = mongoTemplate.getCollection(collectionName);
        for (SPAAddonLine spaAddonLine : spaAddonLineList) {
            //清除航班
            removeObject.put("flightDate", flightDate);
            removeObject.put("type", type);
            removeObject.put("depCity", spaAddonLine.getDepCity());
            removeObject.put("arrCity", spaAddonLine.getArrCity());
            collection.deleteMany(removeObject);
        }
    }
}
