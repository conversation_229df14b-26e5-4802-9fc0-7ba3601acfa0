package com.juneyaoair.flightbasic.request.wechat;


import com.juneyaoair.flightbasic.common.BaseEntityDTO;
import com.juneyaoair.flightbasic.response.city.TCityLaberInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2016-05-18
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SpecialAirlineDTO",description = "特价航线DTO")
public class SpecialAirlineDTO extends BaseEntityDTO implements Serializable {
    @ApiModelProperty(value = "出发城市名称")
    private String depCity;
    @ApiModelProperty(value = "出发城市三字码")
    private String depCityCode;
    @ApiModelProperty(value = "出发城市国内国际",allowableValues = "N,I",notes = "N-国内，I-国际")
    private String depCityIsInter;
    @ApiModelProperty(value = "出发城市拼音")
    private String depCityPinyin;
    @ApiModelProperty(value = "起飞城市英文名")
    private String depCityEn;
    @ApiModelProperty(value = "到达城市名称")
    private String arrCity;
    @ApiModelProperty(value = "到达城市三字码")
    private String arrCityCode;
    @ApiModelProperty(value = "到达城市国内国际",allowableValues = "N,I",notes = "N-国内，I-国际")
    private String arrCityIsInter;
    @ApiModelProperty(value = "到达城市拼音")
    private String arrCityPinyin;
    @ApiModelProperty(value = "到达城市英文名")
    private String arrCityEn;
    private String arrCityUrl;
    @ApiModelProperty(value = "生效日期",notes = "格式:yyyy-MM-dd")
    private String startTime;
    @ApiModelProperty(value = "失效日期",notes = "格式:yyyy-MM-dd")
    private String endTime;
    private double price;
    @ApiModelProperty(value = "低价日期",notes = "格式:yyyy-MM-dd")
    private String activeTime;
    private Integer sort;
    @ApiModelProperty(value = "移动端图片")
    private  String picture;
    @ApiModelProperty(value = "PC端图片")
    private  String picturePc;
    @ApiModelProperty(value = "出发城市标签")
    private  List<TCityLaberInfoDTO> depsubTitle;
    @ApiModelProperty(value = "到达城市标签")
    private  List<TCityLaberInfoDTO> arrsubTitle;

    @ApiModelProperty(value = "图片信息清单")
    private List<PicInfo> picInfoList;

}
