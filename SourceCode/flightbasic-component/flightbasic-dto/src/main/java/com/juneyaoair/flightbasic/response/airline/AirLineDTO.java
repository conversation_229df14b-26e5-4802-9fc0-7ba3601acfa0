package com.juneyaoair.flightbasic.response.airline;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(value = "AirLineDTO",description = "AirLineDTO传输类")
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AirLineDTO {
    private String airlineId;
    @ApiModelProperty(value = "出发机场三字码")
    private String depAirport;
    @ApiModelProperty(value = "出发城市三字码")
    private String depCity;
    @ApiModelProperty(value = "到达机场三字码")
    private String arrAirport;
    @ApiModelProperty(value = "到达城市三字码")
    private String arrCity;
    @ApiModelProperty(value = "承运航司")
    private String carrierCompany;
    @ApiModelProperty(value = "国内/国际或地区标识",example = "D-国内,I-国际或地区")
    private String isInternationalAirline;
    @ApiModelProperty(value = "直达中转标识",example = "Y-中转,N-直达")
    private String isTransit;
    @ApiModelProperty(value = "中转城市")
    private String transitCity;
    @ApiModelProperty(value = "中转机场")
    private String transitAirport;
    @ApiModelProperty(value = "热门航线标识")
    private String isHoLine;
    private String airlineFrontRemark;
    @ApiModelProperty(value = "航线标签集合")
    private List<AirLineLabelDTO> labels;
    @ApiModelProperty(value = "航线类别",allowableValues = "Y,N,S",example = "N-普通航线,Y-AddOn,S-SPA")
    private String addonRemark;
    @ApiModelProperty(value = "出发机场信息")
    private SimplifyAirportDTO depAirportInfo;
    @ApiModelProperty(value = "到达机场信息")
    private SimplifyAirportDTO arrAirportInfo;
}
