package com.juneyaoair.flightbasic.model.basic;

import lombok.AllArgsConstructor;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;


/**
 * @program: flightbasic
 * @description
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2019-07-16 19:04
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name="T_FLIGHT_DYNAMIC_CONCERN")
@EqualsAndHashCode
public class FollowAirLinePO {
    @Id
    @Column(name = "ID")
    private String id;

    @Column(name = "FFP_ID")
    private String ffpId;

    @Column(name = "FFP_CARD_NO")
    private String ffpCardNo;

    @Column(name = "CREATE_TIME")
    private Date createTime;

    @Column(name = "LAST_MODIFY_TIME")
    private Date lastModifyTime;

    /** 是否关注1关注，2不关注 */
    @Column(name = "CONCERN")
    private String  concern;

    @Column(name = "FLIGHT_NO")
    private String flightNo;

    @Column(name = "FLIGHT_DATE")
    private String flightDate;

    @Column(name = "DEP_AIRPORT_CODE")
    private String depAirportCode;

    @Column(name = "ARR_AIRPORT_CODE")
    private String arrAirportCode;

}
