<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--namespace映射mapper-->
<mapper namespace="com.juneyaoair.flightbasic.mapper.TAirportCheckinChannelTimeMapper">
    <!--映射TAirportCheckinSetDO-->
    <resultMap id="airportcheckInChannelTimeResultMap" type="com.juneyaoair.flightbasic.model.TAirportCheckinChannelTimeDO">
        <id column="ID" jdbcType="INTEGER" property="id" />
        <result column="AIRPORT_CODE" jdbcType="VARCHAR" property="airportCode" />
        <result column="CHANNELNO" jdbcType="VARCHAR" property="channelno" />
        <result column="OPEN_FRE" jdbcType="VARCHAR" property="openFre" />
        <result column="OPEN_END" jdbcType="VARCHAR" property="openEnd" />
        <result column="ENABLE" jdbcType="INTEGER" property="enable" />
        <result column="CREATE_TIME" jdbcType="VARCHAR" property="createTime" />
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="UPDATE_TIME" jdbcType="VARCHAR" property="updateTime" />
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
        <result column="DEL_FLG" jdbcType="INTEGER" property="delFlg" />
        <result column="OPEN_DESC" jdbcType="VARCHAR" property="openDesc" />
    </resultMap>
</mapper>