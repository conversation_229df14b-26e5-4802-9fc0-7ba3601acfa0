package com.juneyaoair.flightbasic.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2019-5-8 17:14
 * @description：
 * @modified By：
 * @version: $
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "T_AIRPORT_CHECKIN_CHANNEL_TIME")
public class TAirportCheckinChannelTimeDO {
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator="select SEQ_AIRPORT_CHECKIN_CHAN_TI.nextval from dual" )
    private Long id;

    @Column(name = "AIRPORT_CODE")
    private String airportCode;

    @Column(name = "CHANNELNO")
    private String channelno;

    @Column(name = "OPEN_FRE")
    private String openFre;

    @Column(name = "OPEN_END")
    private String openEnd;

    @Column(name = "ENABLE")
    private Integer enable;

    @Column(name = "CREATE_TIME")
    private Date createTime;

    @Column(name = "CREATE_USER")
    private String createUser;

    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    @Column(name = "UPDATE_USER")
    private String updateUser;

    @Column(name = "DEL_FLG")
    private Integer delFlg;

    @Column(name = "OPEN_DESC")
    private String openDesc;
}
